// Test that Model webhooks don't include role mentions
const { getDb } = require('./src/db/mongo');
const { createModelEmbed, sendWebhook } = require('./src/discord/webhook');
const sentBetsTracker = require('./src/services/sentBetsTracker');
const moment = require('moment-timezone');

async function testModelWebhookNoRole() {
  const db = await getDb();
  const currentTime = moment().toDate();

  console.log('Testing Model webhook without role mentions...');

  // Query the dedicated model_edges collection
  const collection = db.collection('model_edges');
  
  const cursor = collection.find({
    $and: [
      { timestamp: { $gt: currentTime } },
      { edge_points: { $gte: 10 } },
      { ev: { $gte: 70 } }
    ]
  }).limit(1);

  for await (const doc of cursor) {
    try {
      console.log('\nProcessing doc:');
      console.log(`  Game: ${doc.game}`);
      console.log(`  Book: ${doc.book}`);
      
      const embedContent = await createModelEmbed(doc);
      console.log('  Embed created successfully');
      
      // Send the webhook and check the logs - should NOT show role mapping message for Model
      console.log('  Sending Model webhook (should NOT include role mention)...');
      await sendWebhook('Model', embedContent, doc.book);
      
      console.log('  ✅ Model webhook sent - check Discord to verify no role ping');
    } catch (err) {
      console.error('  ❌ Error:', err);
    }
  }

  process.exit(0);
}

// Initialize and run
sentBetsTracker.init().then(() => {
  testModelWebhookNoRole();
});