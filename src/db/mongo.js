// File: src/db/mongo.js
const { MongoClient } = require('mongodb');
const config = require('../config');

let client = null;

async function connect() {
  if (client) return client;
  client = new MongoClient(config.mongoUri);
  await client.connect();
  return client;
}

async function getDb() {
  const client = await connect();
  const db = client.db(config.dbName);
  console.log(`Connected to database: ${config.dbName}`); // This line will now log the correct DB name
  return db;
}

module.exports = { getDb };
