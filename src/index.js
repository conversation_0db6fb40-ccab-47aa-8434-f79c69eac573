// File: src/index.js
const { initializeMonitoring, runModelBatch } = require('./services/betMonitor');
const sentBetsTracker = require('./services/sentBetsTracker');
const logger = require('./utils/logger');
const schedule = require('node-schedule');

async function start() {
  await sentBetsTracker.init();
  const monitorCollections = await initializeMonitoring();
  
  let isRunning = false;
  
  async function runJob() {
    if (isRunning) {
      logger.warn('Previous job still running, skipping this run');
      setTimeout(runJob, 60000);
      return;
    }
    
    isRunning = true;
    try {
      logger.info('Starting job run at:', new Date().toISOString());
      await monitorCollections();
      logger.info('Job completed at:', new Date().toISOString());
    } catch (error) {
      logger.error('Error in job run:', error);
    } finally {
      isRunning = false;
    }
    
    // Schedule the next run after completion
    setTimeout(runJob, 60000); // Run every 60 seconds
  }

  // Start the first job
  runJob();

  // Schedule Tuesday morning (8 AM ET) weekly Model batch for model_edges only
  schedule.scheduleJob({ rule: '0 8 * * 2', tz: 'America/New_York' }, async () => {
    try {
      logger.info('Starting Tuesday 8 AM ET Model batch...');
      await runModelBatch();
      logger.info('Model batch completed.');
    } catch (error) {
      logger.error('Error in Tuesday Model batch:', error);
    }
  });

  logger.info('Game Line Monitor is running...');
}

start().catch(error => {
  logger.error('Error starting the application:', error);
  process.exit(1);
});