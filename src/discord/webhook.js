// File: src/discord/webhook.js
const { WebhookClient, EmbedBuilder, AttachmentBuilder } = require('discord.js');
const config = require('../config');
const { generateHighEvImage } = require('../services/highEvHtmlGenerator');
const { chartGenerator } = require('../services/chartGenerator');
const moment = require('moment-timezone');  // Add this line
const FormData = require('form-data');
const { normalizeName } = require('../config/discordRoles');

const webhooks = Object.entries(config.webhooks).reduce((acc, [key, url]) => {
    if (url && url.startsWith('https://discord.com/api/webhooks/')) {
      try {
        acc[key] = new WebhookClient({ url });
      } catch (error) {
        console.error(`Error: Invalid webhook URL for ${key}: ${error.message}`);
      }
    } else if (url) {
      console.error(`Error: Invalid webhook URL format for ${key}. URLs must start with 'https://discord.com/api/webhooks/'`);
    } else {
      console.warn(`Warning: Webhook URL for ${key} is not set.`);
    }
    return acc;
  }, {});

function getEmbedColor(ev) {
  if (ev >= 0.03) return '#39ff14'; // green for >= 3%
  return '#ff6f00'; // orange for 1-3%
}

function createMobileNotificationContent(embed) {
  // Extract key information for mobile notifications
  const title = embed.data?.title || '';
  const description = embed.data?.description || '';
  
  // Extract EV and odds from fields
  let evInfo = '';
  let oddsInfo = '';
  let isModelEmbed = false;
  
  if (embed.data?.fields) {
    // Check if this is a Model embed by looking for "Model" in field names
    isModelEmbed = embed.data.fields.some(f => f.name && f.name.includes('Model'));
    
    // Find the field containing EV information
    const evField = embed.data.fields.find(f => f.name.includes('EV'));
    if (evField && evField.value) {
      // Extract EV value
      const evMatch = evField.value.match(/EV: ([^\n]+)/);
      if (evMatch) evInfo = evMatch[1].trim();
      
      // Extract bet odds
      const oddsMatch = evField.value.match(/Bet Odds: ([^\n]+)/);
      if (oddsMatch) oddsInfo = oddsMatch[1].trim();
    }
    
    // Fallback: try to parse Edge or Diff % if EV is not present
    if (!evInfo && embed.data?.fields) {
      for (const f of embed.data.fields) {
        // Try to match "Edge:" first (for Model embeds)
        const edgeMatch = f.value && f.value.match(/Edge:\s*([^\n]+)/i);
        if (edgeMatch) {
          evInfo = edgeMatch[1].trim();
          break;
        }
        // Fallback to "Diff %" for backward compatibility
        const diffMatch = f.value && f.value.match(/Diff %:\s*([^\n]+)/i);
        if (diffMatch) {
          evInfo = diffMatch[1].trim();
          break;
        }
      }
    }
    
    // Fallback: try to extract Bet Odds from any field if not found yet
    if (!oddsInfo && embed.data?.fields) {
      for (const f of embed.data.fields) {
        const oddsMatch = f.value && f.value.match(/Bet Odds:\s*([^\n]+)/i);
        if (oddsMatch) {
          oddsInfo = oddsMatch[1].trim();
          break;
        }
      }
    }
  }
  
  // Create concise notification text
  // Format: "League - Selection Market (Odds) • EV: X%"
  let notificationText = title;
  
  if (oddsInfo) {
    // Extract just the selection part (before the market info)
    const titleParts = title.split(' - ');
    if (titleParts.length >= 2) {
      const selectionAndMarket = titleParts[1];
      // Try to extract just the selection (before parentheses or market type)
      const selectionMatch = selectionAndMarket.match(/^([^(]+)/);
      const selection = selectionMatch ? selectionMatch[1].trim() : selectionAndMarket;
      notificationText = `${titleParts[0]} - ${selection} (${oddsInfo})`;
    }
  }
  
  if (evInfo) {
    // Use "Edge" for Model embeds, "EV" for regular embeds
    const label = isModelEmbed ? 'Edge' : 'EV';
    notificationText += ` • ${label}: ${evInfo}`;
  }
  
  // Keep it concise for mobile notifications (max ~100 chars)
  if (notificationText.length > 100) {
    // Truncate smartly
    const parts = notificationText.split(' • ');
    if (parts.length > 1 && parts[0].length > 80) {
      // Shorten the first part and keep the EV
      notificationText = parts[0].substring(0, 77) + '...' + ' • ' + parts[1];
    }
  }
  
  return notificationText;
}


async function createEmbed(doc, lastSentData) {
  const evValue = parseFloat(doc.ev);
  // Calculate win percentage - use sharp book's win percentage if available
  let winPercentage = '0.0';
  if (doc.win_percent && !isNaN(doc.win_percent)) {
    winPercentage = (doc.win_percent * 100).toFixed(1);
  }
  const fvValue = doc.fv > 0 ? `+${doc.fv}` : `${doc.fv}`;
  const pinnacleLimit = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(doc.pinnacle_limits);

  // --- Title Generation Logic ---
  const cleanedMarketDisplay = doc.market_display.replace(/[()]/g, ''); // Remove parentheses from market display
  let embedTitle = `${doc.league} - ${doc.selection} ${cleanedMarketDisplay}`; // Base title structure

  // Add NoVig limit to the title if applicable
  if (doc.book === 'NoVig' && doc.novig_limit !== undefined && doc.novig_limit !== null) {
    const formattedNovigLimit = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(doc.novig_limit);
    embedTitle += ` (Limit: ${formattedNovigLimit})`; // Append limit for NoVig
  }
  // Add ProphetX limit to the title if applicable
  else if (doc.book === 'ProphetX' && doc.prophetx_stake_limit !== undefined && doc.prophetx_stake_limit !== null) {
    const formattedProphetXLimit = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(doc.prophetx_stake_limit);
    embedTitle += ` (Limit: ${formattedProphetXLimit})`; // Append limit for ProphetX
  }
  // -----------------------------

  // Revert field name logic, as limit is now in title
  const firstFieldName = `${doc.book} EV`;

  let pinnacleOddsField = null;
  
  // Only create Pinnacle field if we have valid Pinnacle odds (not NaN or null)
  if (doc.pinnacle_odds_american_our_side && 
      doc.pinnacle_odds_american_our_side !== 'NaN' && 
      !isNaN(doc.pinnacle_odds_american_our_side)) {
    
    if (lastSentData && lastSentData.last_pinnacle_odds_american_our_side !== doc.pinnacle_odds_american_our_side) {
      // Show odds change from last sent notification
      pinnacleOddsField = {
        name: 'Pinnacle',
        value: `Odds: ${lastSentData.last_pinnacle_odds_american_our_side} / ${lastSentData.last_pinnacle_odds_american_opposite_side} :arrow_right: **${doc.pinnacle_odds_american_our_side} / ${doc.pinnacle_odds_american_opposite_side}**\nWin %: ${winPercentage}%\nLimit: ${pinnacleLimit}`
      };
    } else {
      pinnacleOddsField = {
        name: 'Pinnacle',
        value: `Odds: ${doc.pinnacle_odds_american_our_side} / ${doc.pinnacle_odds_american_opposite_side}\nWin %: ${winPercentage}%\nLimit: ${pinnacleLimit}`
      };
    }
  }

  const embed = new EmbedBuilder()
    .setColor(getEmbedColor(evValue))
    .setTitle(embedTitle) // Use the newly constructed title
    .setDescription(`${doc.game} - ${doc.formatted_timestamp}`)
    .setAuthor({
      name: doc.book,
      iconURL: config.images[doc.book],
    })
    .setThumbnail(config.images.edgezoneThumbnail);

  // Add base fields
  const fields = [
    {
      name: firstFieldName, // Reverted to default field name
      value: `Bet Odds: ${doc.odds_american}\nFV: ${fvValue}\nEV: ${doc.ev_display}\nQK: ${doc.qk_display}`
    }
  ];

  // Add Pinnacle field if it exists
  if (pinnacleOddsField) {
    fields.push(pinnacleOddsField);
  }

  // Add Circa odds field if available
  if (doc.circa_odds_american_our_side && doc.circa_odds_american_opposite_side) {
    fields.push({
      name: 'Circa',
      value: `Odds: ${doc.circa_odds_american_our_side} / ${doc.circa_odds_american_opposite_side}`
    });
  }

  embed.addFields(...fields)
    .setFooter({
      text: `Powered by Edge Zone`,
      iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail
    })
    .setTimestamp();

  // Generate chart on-demand if not present
  if (!doc.odds_chart_base64) {
    console.log('[WEBHOOK] Generating chart for', doc.selection);
    doc.odds_chart_base64 = await chartGenerator.generateOddsChart(doc);
  }

  // Add chart if available
  if (doc.odds_chart_base64) {
    embed.setImage('attachment://odds_movement.png');
  }

  // Add bet link and event link as clickable links without field names
  let linkContent = '';
  if (doc.bet_link) {
    linkContent += `[Place Bet](${doc.bet_link})`;
  }
  if (doc.event_link) {
    if (linkContent) linkContent += ' | ';
    linkContent += `[Place Bet](${doc.event_link})`;
  }
  if (linkContent) {
    embed.addFields({ name: '\u200B', value: linkContent });
  }

  const embedContent = { embed };
  
  // Add chart attachment if available
  if (doc.odds_chart_base64) {
    try {
      console.log('[WEBHOOK] Processing odds chart, base64 length:', doc.odds_chart_base64.length);
      const imageBuffer = Buffer.from(doc.odds_chart_base64, 'base64');
      console.log('[WEBHOOK] Created image buffer, size:', imageBuffer.length);
      embedContent.files = [{
        attachment: imageBuffer,
        name: 'odds_movement.png'
      }];
    } catch (error) {
      console.error('Error processing odds chart:', error);
    }
  }

  return embedContent;
}

async function createModelEmbed(doc) {
  const canonicalBook = normalizeName(doc.book) || doc.book;
  const authorIcon = config.images[canonicalBook] || config.images.edgezoneThumbnail;

  // Derive market label from doc.market or market_type
  let marketLabel = '';
  const marketLower = (doc.market || '').toLowerCase();
  const marketTypeLower = (doc.market_type || '').toLowerCase();
  
  // Check if this is a totals/over-under bet
  const isTotal = marketLower === 'over_under' || 
                  marketLower === 'total' || 
                  marketLower === 'gametotal' ||
                  marketTypeLower.includes('total');
  
  if (isTotal) {
    marketLabel = ''; // Don't add "Total" to the title for over/under
  } else if (marketLower === 'spread' || marketLower === 'gamespread' || marketTypeLower.includes('spread')) {
    marketLabel = 'Spread';
  } else if (marketLower === 'moneyline' || marketTypeLower.includes('money')) {
    marketLabel = 'Moneyline';
  } else if (doc.market) {
    marketLabel = doc.market;
  } else if (doc.market_type) {
    marketLabel = doc.market_type;
  }

  // Format selection - capitalize if it's a side like "away" or "home" or "over" or "under"
  const formattedSelection = doc.selection
    ? doc.selection.charAt(0).toUpperCase() + doc.selection.slice(1).toLowerCase()
    : '';

  // Add proper sign to lines
  const formatLine = (line) => {
    if (line == null) return null;
    const numLine = parseFloat(line);
    return numLine > 0 ? `+${line}` : `${line}`;
  };

  // Format timestamp
  const ts = doc.timestamp ? moment(doc.timestamp).tz('America/New_York').format('ddd, MMM D, h:mm A z') : '';
  
  // Build title based on market type
  let title;
  if (isTotal) {
    // For totals, format as "NCAAF - Over 53.5" (no market label needed)
    title = `${doc.league || ''} - ${formattedSelection} ${doc.book_line || ''}`.replace(/\s+/g, ' ').trim();
  } else {
    // For spreads and moneylines, include market label and formatted line
    const formattedBookLine = formatLine(doc.book_line);
    title = `${doc.league || ''} - ${formattedSelection} ${marketLabel || ''} ${formattedBookLine || ''}`.replace(/\s+/g, ' ').trim();
  }

  // Build description
  const description = `${doc.game || ''}${ts ? ' - ' + ts : ''}`;

  // Build the detailed field with model-specific information
  const fields = [{
    name: `${canonicalBook} Model`,
    value: [
      doc.ev_display ? `Edge: ${doc.ev_display}` : null
    ].filter(Boolean).join('\n')
  }];

  const embed = new EmbedBuilder()
    .setColor('#00b894') // Distinct color for model embeds
    .setTitle(title)
    .setDescription(description)
    .setAuthor({ name: 'EL\nEDGE ZONE', iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setThumbnail(config.images.edgezoneThumbnail)
    .addFields(...fields)
    .setFooter({ text: 'Powered by Edge Zone', iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setTimestamp();

  return { embed };
}

async function createGroupedEmbed(bets) {
  // Sort bets by EV% in descending order
  bets.sort((a, b) => parseFloat(b.ev) - parseFloat(a.ev));

  const firstBet = bets[0];
  const pinnacleLimit = !isNaN(firstBet.pinnacle_limits) && firstBet.pinnacle_limits !== null ? 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(firstBet.pinnacle_limits) : 
    'N/A';
  
  const embed = new EmbedBuilder()
    .setColor(getEmbedColor(parseFloat(firstBet.ev))) // Use the highest EV for color
    .setTitle(`${firstBet.game} - ${firstBet.market_display}`)
    .setDescription(`${firstBet.formatted_timestamp}`)
    .setAuthor({
      name: `${firstBet.book} | ${firstBet.league}`,
      iconURL: config.images[firstBet.book],
    })
    .setThumbnail(config.images.edgezoneThumbnail);

  let betContent = '';
  bets.forEach((bet) => {
    const winPercentage = (bet.win_percent * 100).toFixed(1);
    // Use the selection from whichever sharp book is being used
    const selectionName = bet.sharp_book_used === 'Circa' ? 
      bet.circa_selection_our_side || bet.selection :
      bet.pinnacle_selection_our_side || bet.selection;
    betContent += `**${selectionName} (${bet.odds_american})**`;
    betContent += `\`\`\``;
    betContent += `EV: ${bet.ev_display.padStart(3)}  QK: ${bet.qk_display.padStart(4)}\n`;
    betContent += `FV: ${String(bet.fv).padStart(3)}  W%: ${winPercentage.padStart(4)}%\n`;
    betContent += `\`\`\`\n`;
  });

  // Add sharp book odds information
  const fields = [{ name: 'Bets', value: betContent }];
  
  // Check if we have valid Pinnacle data
  if (firstBet.pinnacle_odds_american_our_side && 
      firstBet.pinnacle_odds_american_our_side !== 'NaN' && 
      !isNaN(firstBet.pinnacle_odds_american_our_side)) {
    // Add Pinnacle field
    let pinnacleInfo = `**Pinnacle**\n`;
    pinnacleInfo += `Odds: ${firstBet.pinnacle_odds_american_our_side} / ${firstBet.pinnacle_odds_american_opposite_side}\n`;
    const winPercentage = (firstBet.win_percent * 100).toFixed(1);
    pinnacleInfo += `Win %: ${winPercentage}%\n`;
    pinnacleInfo += `Limit: ${pinnacleLimit}`;
    fields.push({ name: '\u200B', value: pinnacleInfo });
  }
  
  // Check if we have Circa data
  if (firstBet.circa_odds_american_our_side && 
      firstBet.circa_odds_american_opposite_side) {
    let circaInfo = `**Circa**\n`;
    circaInfo += `Odds: ${firstBet.circa_odds_american_our_side} / ${firstBet.circa_odds_american_opposite_side}`;
    fields.push({ name: '\u200B', value: circaInfo });
  }
  
  embed.addFields(...fields);

  // Generate chart on-demand for first bet if not present
  if (!firstBet.odds_chart_base64) {
    console.log('[WEBHOOK] Generating chart for grouped bet', firstBet.selection);
    firstBet.odds_chart_base64 = await chartGenerator.generateOddsChart(firstBet);
  }

  // Add chart if available (use first bet's chart)
  if (firstBet.odds_chart_base64) {
    embed.setImage('attachment://odds_movement.png');
  }

  // Add bet link and event link as clickable links without field names for the first bet
  let linkContent = '';
  if (firstBet.bet_link) {
    linkContent += `[Place Bet](${firstBet.bet_link})`;
  }
  if (firstBet.event_link) {
    if (linkContent) linkContent += ' | ';
    linkContent += `[Place Bet](${firstBet.event_link})`;
  }
  if (linkContent) {
    embed.addFields({ name: '\u200B', value: linkContent });
  }

  embed.setFooter({
    text: `Powered by Edge Zone`,
    iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail
  })
  .setTimestamp();

  const embedContent = { embed };
  
  // Add chart attachment if available
  if (firstBet.odds_chart_base64) {
    try {
      const imageBuffer = Buffer.from(firstBet.odds_chart_base64, 'base64');
      embedContent.files = [{
        attachment: imageBuffer,
        name: 'odds_movement.png'
      }];
    } catch (error) {
      console.error('Error processing odds chart:', error);
    }
  }

  return embedContent;
}

async function createConsolidatedModelEmbed(docs) {
  // Sort for display consistency (by EV desc, then book name)
  const sorted = [...docs].sort((a, b) => {
    const evA = typeof a.ev === 'number' ? a.ev : parseFloat(a.ev) || 0;
    const evB = typeof b.ev === 'number' ? b.ev : parseFloat(b.ev) || 0;
    if (evB !== evA) return evB - evA;
    const bookA = (normalizeName(a.book) || a.book || '').toString();
    const bookB = (normalizeName(b.book) || b.book || '').toString();
    return bookA.localeCompare(bookB);
  });

  const doc = sorted[0];
  const marketLower = (doc.market || '').toLowerCase();
  const marketTypeLower = (doc.market_type || '').toLowerCase();

  const isTotal = marketLower === 'over_under' ||
                  marketLower === 'total' ||
                  marketLower === 'gametotal' ||
                  marketTypeLower.includes('total');

  const formatLine = (line, isSpread) => {
    if (line == null || Number.isNaN(parseFloat(line))) return 'ML';
    const numLine = parseFloat(line);
    if (!isSpread) return `${line}`;
    return numLine > 0 ? `+${line}` : `${line}`;
  };

  // Determine market label for title (do not show 'Total' for O/U)
  let marketLabel = '';
  if (!isTotal) {
    if (marketLower === 'spread' || marketLower === 'gamespread' || marketTypeLower.includes('spread')) {
      marketLabel = 'Spread';
    } else if (marketLower === 'moneyline' || marketTypeLower.includes('money')) {
      marketLabel = 'Moneyline';
    } else if (doc.market) {
      marketLabel = doc.market;
    } else if (doc.market_type) {
      marketLabel = doc.market_type;
    }
  }

  const formattedSelection = doc.selection
    ? doc.selection.charAt(0).toUpperCase() + doc.selection.slice(1).toLowerCase()
    : '';

  // Title
  let title;
  if (isTotal) {
    title = `${doc.league || ''} - ${formattedSelection} ${doc.book_line || ''}`.replace(/\s+/g, ' ').trim();
  } else {
    const isSpread = marketLabel.toLowerCase().includes('spread') || marketTypeLower.includes('spread');
    const formattedBookLine = formatLine(doc.book_line, isSpread);
    title = `${doc.league || ''} - ${formattedSelection} ${marketLabel || ''} ${formattedBookLine || ''}`.replace(/\s+/g, ' ').trim();
  }

  // Description: Game info with time in ET
  const ts = doc.timestamp ? moment(doc.timestamp).tz('America/New_York').format('ddd, MMM D, h:mm A z') : '';
  const description = `${doc.game || ''}${ts ? ' - ' + ts : ''}`;

  // Build Sportsbooks lines: "BookName Line Odds EdgePercent"
  const sportsbookLines = sorted.map(d => {
    const book = normalizeName(d.book) || d.book || '';
    const isSpread = !isTotal && (marketLower === 'spread' || marketLower === 'gamespread' || marketTypeLower.includes('spread'));
    const lineStr = d.book_line != null ? formatLine(d.book_line, isSpread) : (isTotal ? '' : 'ML');
    const odds = d.odds_american != null ? d.odds_american : (d.book_price != null ? d.book_price : '');
    const edge = d.ev_display ? d.ev_display : (typeof d.ev === 'number' ? `${d.ev.toFixed(2)}%` : '');
    return [book, lineStr, odds, edge].filter(Boolean).join(' ');
  }).join('\n');

  const nowEt = moment().tz('America/New_York').format('h:mm A z');

  const embed = new EmbedBuilder()
    .setColor('#00b894')
    .setTitle(title)
    .setDescription(description)
    .setAuthor({ name: 'EL\nEDGE ZONE', iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setThumbnail(config.images.edgezoneThumbnail)
    .addFields({ name: 'Sportsbooks', value: sportsbookLines || 'No books found' })
    .setFooter({ text: `Powered by Edge Zone | Today at ${nowEt}`, iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setTimestamp();

  return { embed };
}

async function sendWebhook(collectionName, content, bookName, isImage = false) {
  if (!webhooks[collectionName]) {
    console.error(`Error: Webhook for ${collectionName} is not configured.`);
    return;
  }
  try {
    // Extract some info from the content for better logging
    let betInfo = 'Unknown';
    if (!isImage && content && content.embed && content.embed.data) {
      if (content.embed.data.title) {
        betInfo = content.embed.data.title;
      }
      if (content.embed.data.description) {
        betInfo += ' | ' + content.embed.data.description;
      }
    }
    
    console.log(`[WEBHOOK] Sending ${collectionName} webhook for "${betInfo}" (Book: ${bookName || 'N/A'})`);
    
    let messageContent = '';
    
    // Skip role mentions for Model webhooks
    if (collectionName !== 'Model') {
      const webhookType = collectionName === 'HighLimit' ? 'HighLimit'
        : collectionName === 'HammerPlays' ? 'HammerPlays'
        : 'Regular';

      // Centralized resolver with alias handling; inline require to avoid import placement issues
      const { getRoleId } = require('../config/discordRoles');
      const roleId = getRoleId(bookName, webhookType);
      if (roleId) {
        messageContent = `<@&${roleId}>`;
      } else if (bookName) {
        console.log(`[WEBHOOK] No role mapping found for book "${bookName}" (type: ${webhookType})`);
      }
    }

    if (isImage) {
      console.log('[WEBHOOK] Sending image content');
      if (!(content instanceof Buffer)) {
        throw new Error('Image content must be a Buffer');
      }
      const attachment = new AttachmentBuilder(content, { name: 'high_ev_bets.png' });
      const payload = {
        files: [attachment]
      };
      if (messageContent?.trim()) {
        payload.content = messageContent.trim();
      }
      await webhooks[collectionName].send(payload);
    } else {
      console.log('[WEBHOOK] Preparing embed content');
      const { embed, files } = content;
      
      // Check if we have files (chart attachment)
      if (files && files.length > 0) {
        console.log('[WEBHOOK] Processing file attachment for chart');
        
        // Ensure embed is valid
        if (!embed || !embed.toJSON) {
          console.error('[WEBHOOK] Invalid embed object:', embed);
          throw new Error('Invalid embed object');
        }

        console.log('[WEBHOOK] Embed JSON:', JSON.stringify(embed.toJSON()).substring(0, 200) + '...');
        
        try {
          // Use discord.js's built-in functionality to handle file attachments
          // This automatically sets up the proper multipart boundaries and headers
          const payload = {
            embeds: [embed], // Pass the EmbedBuilder instance directly
            files: files.map(file => {
              console.log(`[WEBHOOK] Processing file: ${file.name}, size: ${file.attachment.length} bytes`);
              return { attachment: file.attachment, name: file.name };
            })
          };
          
          // Create mobile-friendly notification content
          const notificationContent = createMobileNotificationContent(embed);
          if (messageContent?.trim()) {
            // Prepend role mention if exists
            payload.content = `${messageContent.trim()} ${notificationContent}`;
          } else {
            payload.content = notificationContent;
          }
          
          await webhooks[collectionName].send(payload);
          
          console.log(`[WEBHOOK] Successfully sent ${collectionName} webhook with chart for "${betInfo}"`);
        } catch (error) {
          console.error(`[WEBHOOK] Discord API error for ${collectionName}:`, error.message);
          throw new Error(`HTTP error! status: ${error.status || 'unknown'}`);
        }
      } else {
        // No files, send normally using discord.js built-in functionality
        
        // Log webhook URL for troubleshooting (mask parts of it for security)
        const webhookUrl = webhooks[collectionName].url;
        const maskedUrl = webhookUrl.replace(/\/([^\/]+)$/, '/***masked***');
        console.log(`[WEBHOOK] Using Discord webhook URL: ${maskedUrl}`);
        
        // Log compact version of the payload
        const compactPayload = {
          contentLength: (messageContent || '').length,
          embedTitle: embed.data.title || 'No Title',
          embedDescriptionLength: (embed.data.description || '').length,
          fieldsCount: (embed.data.fields || []).length
        };
        console.log('[WEBHOOK] Payload summary:', JSON.stringify(compactPayload));
        
        try {
          // Use the discord.js WebhookClient to send the message
          // This handles all the proper formatting and headers automatically
          const payload = {
            embeds: [embed] // Pass the EmbedBuilder instance directly
          };
          
          // Create mobile-friendly notification content
          const notificationContent = createMobileNotificationContent(embed);
          if (messageContent?.trim()) {
            // Prepend role mention if exists
            payload.content = `${messageContent.trim()} ${notificationContent}`;
          } else {
            payload.content = notificationContent;
          }
          
          await webhooks[collectionName].send(payload);
          
          console.log(`[WEBHOOK] Successfully sent ${collectionName} webhook for "${betInfo}"`);
          
          // Note: Rate limit information is still accessible through webhookClient.rateLimitStatus 
          // but requires additional code to access it in this approach
        } catch (error) {
          console.error(`[WEBHOOK] Discord API error for ${collectionName}:`, error.message);
          throw new Error(`Discord webhook error: ${error.message}`);
        }
      }
    }
  } catch (error) {
    console.error(`[WEBHOOK] Error sending ${collectionName} webhook:`, error);
    console.error(error.stack); // Log the full stack trace
  }
}

async function sendHighEvWebhook(bets) {
  const webhookUrl = config.webhooks.HighEV || process.env.DISCORD_WEBHOOK_URL;
  
  if (!webhookUrl) {
    console.error('Error: High EV webhook URL is not configured.');
    return;
  }

  const webhookClient = new WebhookClient({ url: webhookUrl });

  try {
    const imageBuffersWithTimestamps = await generateHighEvImage(bets);

    for (const { imageBuffer, timestamp, pageNumber, totalPages } of imageBuffersWithTimestamps) {
      // Discord timestamp format for time only
      const discordTimestamp = `<t:${timestamp}:t>`;
      
      await webhookClient.send({
        content: `${discordTimestamp} (Page ${pageNumber}/${totalPages})`,
        files: [{ attachment: imageBuffer, name: 'high-ev-bets.png' }]
      });
    }

    console.log('High EV webhook sent successfully');
  } catch (error) {
    console.error('Error sending high EV webhook:', error);
  }
}

module.exports = { createEmbed, createGroupedEmbed, sendWebhook, sendHighEvWebhook, createModelEmbed, createConsolidatedModelEmbed, createMobileNotificationContent };