/**
 * Build axis ticks based on standard American odds values
 * This ensures we get unique, meaningful odds labels on the y-axis
 */

// Standard American odds values used in betting
const STANDARD_ODDS = [
  // Extreme underdogs
  +1000, +900, +800, +700, +600, +550, +500, +450, +400, +350, +300, +275, +250, +225, +200,
  +190, +180, +170, +160, +150, +140, +130, +120, +110, +105, +100,
  // Favorites
  -105, -110, -120, -130, -140, -150, -160, -170, -180, -190, -200,
  -225, -250, -275, -300, -350, -400, -450, -500, -550, -600, -700, -800, -900, -1000
];

/**
 * Convert American odds to probability
 */
function oddsToProb(odds) {
  if (odds >= 100) {
    return 100 / (odds + 100);
  } else {
    return -odds / (-odds + 100);
  }
}

/**
 * Build y-axis ticks for odds chart
 * Returns an array of objects with { prob, odds, label }
 * @param {number} minProb - Minimum probability in the data
 * @param {number} maxProb - Maximum probability in the data
 * @param {number} maxTicks - Maximum number of ticks to show (default 10)
 */
function buildOddsTicks(minProb, maxProb, maxTicks = 10) {
  // Convert min/max probabilities to approximate odds ranges
  const minOdds = probToApproxOdds(maxProb); // max prob = min odds (more negative)
  const maxOdds = probToApproxOdds(minProb); // min prob = max odds (more positive)
  
  // Filter standard odds to those within our range
  const validOdds = STANDARD_ODDS.filter(odds => {
    if (minOdds < 0 && maxOdds < 0) {
      // Both negative
      return odds <= maxOdds && odds >= minOdds;
    } else if (minOdds > 0 && maxOdds > 0) {
      // Both positive
      return odds >= minOdds && odds <= maxOdds;
    } else {
      // Crosses even odds
      return (odds < 0 && odds >= minOdds) || (odds > 0 && odds <= maxOdds);
    }
  });
  
  // If we have too many valid odds, thin them out
  let selectedOdds = validOdds;
  if (validOdds.length > maxTicks) {
    // Calculate step to get approximately maxTicks values
    const step = Math.ceil(validOdds.length / maxTicks);
    selectedOdds = validOdds.filter((_, index) => index % step === 0);
    
    // Always include the first and last odds if not already included
    if (selectedOdds.indexOf(validOdds[0]) === -1) {
      selectedOdds.unshift(validOdds[0]);
    }
    if (selectedOdds.indexOf(validOdds[validOdds.length - 1]) === -1) {
      selectedOdds.push(validOdds[validOdds.length - 1]);
    }
  }
  
  // Convert selected odds to tick objects
  const ticks = selectedOdds.map(odds => ({
    prob: oddsToProb(odds),
    odds: odds,
    label: odds > 0 ? `+${odds}` : `${odds}`
  }));
  
  // Sort by probability (for correct y-axis ordering)
  ticks.sort((a, b) => a.prob - b.prob);
  
  return ticks;
}

/**
 * Convert probability to approximate American odds
 * (used for finding the range of standard odds to display)
 */
function probToApproxOdds(prob) {
  if (prob <= 0 || prob >= 1) return null;
  if (prob > 0.5) {
    return -Math.round(100 * prob / (1 - prob));
  }
  return Math.round(100 * (1 - prob) / prob);
}

/**
 * Format axis bounds and ticks for ECharts
 * Returns { min, max, ticks } where ticks is an array of probability values
 */
function formatAxisConfig(minProb, maxProb, maxTicks = 10) {
  const tickData = buildOddsTicks(minProb, maxProb, maxTicks);
  
  if (tickData.length === 0) {
    // Fallback to simple probability ticks
    return {
      min: minProb,
      max: maxProb,
      ticks: null // Let ECharts handle it
    };
  }
  
  // Extract just the probability values for axis ticks
  const probTicks = tickData.map(t => t.prob);
  
  // Extend bounds slightly beyond data if needed
  const axisMin = Math.min(minProb * 0.98, probTicks[0]);
  const axisMax = Math.max(maxProb * 1.02, probTicks[probTicks.length - 1]);
  
  return {
    min: axisMin,
    max: axisMax,
    ticks: probTicks,
    tickData: tickData // Include full tick data for label formatting
  };
}

module.exports = { 
  buildOddsTicks, 
  formatAxisConfig,
  oddsToProb
};