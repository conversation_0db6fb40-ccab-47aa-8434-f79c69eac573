/**
 * Build probability ticks to yield ~5-7 steps between minP and maxP, returning an ordered array of probability values.
 */
function buildProbTicks(minP, maxP) {
  const STEPS = [0.01, 0.0125, 0.025, 0.05];
  const span = maxP - minP;

  // Pick the largest step that yields about 5-7 ticks across the span
  let step = STEPS[STEPS.length - 1];
  for (const s of STEPS) {
    if ((span / s) <= 7) {
      step = s;
      break;
    }
  }

  const ticks = [];
  let current = minP;
  // Build the ticks in increments of step
  while (current <= maxP + 1e-12) {
    ticks.push(current);
    current += step;
  }

  return ticks;
}

/**
 * Convert a probability (0..1) to American odds, returning null if out of range.
 */
function probToAmerican(prob) {
  if (prob <= 0 || prob >= 1) return null;
  // For > 0.5 => negative line
  if (prob > 0.5) {
    return -Math.round((100 * prob) / (1 - prob));
  }
  // Otherwise positive line
  return Math.round((100 * (1 - prob)) / prob);
}

/**
 * Format an American-odds string from a probability, adding a "+" if positive.
 * No snapping or near-even logic here—just a direct call.
 */
function formatOddsFromProb(prob) {
  const odds = probToAmerican(prob);
  if (odds === null) return '';
  return odds > 0 ? `+${odds}` : `${odds}`;
}

module.exports = { buildProbTicks, formatOddsFromProb };