require('dotenv').config();
const imageUrls = require('./config/imageUrls');

console.log('Loading configuration...');
console.log('HIGH_LIMIT_WEBHOOK_URL:', process.env.HIGH_LIMIT_WEBHOOK_URL);
// console.log('DB_NAME:', process.env.DB_NAME); // Add this line to log the DB_NAME

module.exports = {
  mongoUri: process.env.MONGO_URI,
  dbName: process.env.DB_NAME || 'edgezone_game_lines_dev', // Provide a default value
  collections: [
    'Fanduel',
    'DraftKings',
    'Fliff',
    'HardRock',
    'Rebet',
    'Bovada',
    'Fanatics',
    'NoVig',
    'HighLimit',
    'ProphetX',
    'Underdog',
    'Caesars',
    'BetOnline',
    'Bet105'
  ],
  webhooks: {
    Fanduel: process.env.FANDUEL_WEBHOOK_URL,
    DraftKings: process.env.DRAFTKINGS_WEBHOOK_URL,
    Fliff: process.env.FLIFF_WEBHOOK_URL,
    HardRock: process.env.HARDROCK_WEBHOOK_URL,
    Rebet: process.env.REBET_WEBHOOK_URL,
    Bovada: process.env.BOVADA_WEBHOOK_URL,
    Fanatics: process.env.FANATICS_WEBHOOK_URL,
    ESPNBet: process.env.ESPNBET_WEBHOOK_URL,
    NoVig: process.env.NOVIG_WEBHOOK_URL,
    HighLimit: process.env.HIGH_LIMIT_WEBHOOK_URL,
    HammerPlays: process.env.HAMMER_PLAYS_WEBHOOK_URL,
    GroupedBets: process.env.GROUPED_BETS_WEBHOOK_URL,
    HighEV: process.env.HIGH_EV_WEBHOOK_URL,
    ProphetX: process.env.PROPHETX_WEBHOOK_URL,
    Underdog: process.env.UNDERDOG_WEBHOOK_URL,
    Caesars: process.env.CAESARS_WEBHOOK_URL,
    BetOnline: process.env.BETONLINE_WEBHOOK_URL,
    Bet105: process.env.BET105_WEBHOOK_URL,
    Model: 'https://discord.com/api/webhooks/1409997143485321308/h_57oDgCaSwFtNk6zLSsz0Vn2JmIY-WdO_2x80crMvfomCApacSZ-D4bosw6mBHnTccc'
  },
  images: {
    // Sportsbook logos
    ...imageUrls.sportsbooks,
    // Edge Zone branding
    edgezoneThumbnail: imageUrls.edgeZone.thumbnail,
    edgezoneLogo: imageUrls.edgeZone.logo
  },
  groupedBetsSettings: { // New configuration for grouped bets
    maxGroupSize: 5
  }
};