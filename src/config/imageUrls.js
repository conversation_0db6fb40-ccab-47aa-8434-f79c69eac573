// Centralized image URLs configuration
// All image URLs are hardcoded here for easy management

module.exports = {
  // Edge Zone Branding
  edgeZone: {
    logo: 'https://raw.githubusercontent.com/datawisebets/logos/main/ChatGPT%20Image%20Jul%2014%2C%202025%2C%2001_41_38%20PM.png',
    thumbnail: 'https://raw.githubusercontent.com/datawisebets/logos/main/ChatGPT%20Image%20Jul%2014%2C%202025%2C%2001_41_38%20PM.png'
  },

  // Sportsbook Logos
  sportsbooks: {
    Fanduel: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fanduel.png',
    DraftKings: 'https://raw.githubusercontent.com/datawisebets/logos/main/DraftKings.png',
    Fliff: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fliff.png',
    HardRock: 'https://raw.githubusercontent.com/datawisebets/logos/main/HardRock.png',
    Rebet: 'https://raw.githubusercontent.com/datawisebets/logos/main/rebet.png',
    Bovada: 'https://raw.githubusercontent.com/datawisebets/logos/main/Bovada.png',
    Fanatics: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fanatics.png',
    ESPNBet: 'https://raw.githubusercontent.com/datawisebets/logos/main/ESPNBet2.png',
    NoVig: 'https://raw.githubusercontent.com/datawisebets/logos/main/novig.png',
    ProphetX: 'https://raw.githubusercontent.com/datawisebets/logos/refs/heads/main/prophetx.webp?raw=true',
    Underdog: 'https://raw.githubusercontent.com/datawisebets/logos/main/Underdog.png',
    Caesars: 'https://raw.githubusercontent.com/datawisebets/logos/main/Caesars.png',
    BetOnline: 'https://raw.githubusercontent.com/datawisebets/logos/main/BetOnline.png',
    HighLimit: 'https://via.placeholder.com/150?text=HighLimit', // Logo not available
    Pinnacle: 'https://raw.githubusercontent.com/datawisebets/logos/main/pinnacle.png',
    Circa: 'https://raw.githubusercontent.com/datawisebets/logos/main/Circa.png'
  }
};