// File: src/config/discordRoles.js

// Canonical mainline/gameline roles (single source of truth)
const mainlineRoles = {
  BetRivers: '1408214610393169980',
  Caesars: '1397326727016878203',
  DraftKings: '1397326636721901679',
  Fanatics: '1397326555025375275',
  FanDuel: '1397326487450947654',
  Fliff: '1397326409193492563',
  HardRock: '1397326271758991454',
  NoVig: '1397326078657298454',
  Rebet: '1397326165156565093',
  ProphetX: '1397327022338084924',
  Underdog: '1397326993808166973',
  BetOnline: '1397326826468278456',
  MGM: '1408240887498281171',
  ESPN: '1408241119640289403',
  Bovada: '1408241184479903815'
};

// Optional props/extra roles (kept separate; not used in this repo)
const propsRoles = {
  PropBuilder: '1397325963288645702',
  Betr: '1397327219570900994',
  Dabble: '1397327243847663777',
  Epick: '1397327350735048865',
  HotStreak: '1397327375733227661',
  OwnersBox: '1397327552548175984',
  Pick6: '1397327671754625226',
  PrizePicks: '1397327737902858362'
};

// Map normalized aliases to canonical keys
// Keys are "normalized" (lowercased, punctuation/space/underscore/hyphen removed)
const aliasMap = {
  // Mainline/gameline aliases
  fanduel: 'FanDuel',
  fd: 'FanDuel',

  draftkings: 'DraftKings',
  dk: 'DraftKings',

  hardrock: 'HardRock',

  fliff: 'Fliff',
  rebet: 'Rebet',

  novig: 'NoVig',
  novigbook: 'NoVig',

  caesars: 'Caesars',

  bettorivers: 'BetRivers',
  betrivers: 'BetRivers',
  rivers: 'BetRivers',

  betonline: 'BetOnline',
  bol: 'BetOnline',

  mgm: 'MGM',
  betmgm: 'MGM',

  espn: 'ESPN',
  espnbet: 'ESPN',

  bovada: 'Bovada',

  fanatics: 'Fanatics',

  prophetx: 'ProphetX',
  'prophet-x': 'ProphetX',

  underdog: 'Underdog',
  ud: 'Underdog',

  // Props/extras aliases
  propbuilder: 'PropBuilder',
  betr: 'Betr',
  dabble: 'Dabble',
  epick: 'Epick',
  hotstreak: 'HotStreak',
  ownersbox: 'OwnersBox',
  pick6: 'Pick6',
  prizepicks: 'PrizePicks'
};

// Normalize strings: remove spaces, hyphens, underscores, periods, apostrophes; lowercase
function normalizeKey(str) {
  return (str || '')
    .toString()
    .trim()
    .toLowerCase()
    .replace(/[\s_\-\.']/g, '');
}

// Resolve a canonical book name from input; returns canonical key or null
function normalizeName(bookName) {
  if (!bookName) return null;

  const norm = normalizeKey(bookName);

  if (aliasMap[norm]) return aliasMap[norm];

  for (const key of Object.keys(mainlineRoles)) {
    if (normalizeKey(key) === norm) return key;
  }

  for (const key of Object.keys(propsRoles)) {
    if (normalizeKey(key) === norm) return key;
  }

  return null;
}

// Get role ID by book and webhook type
// webhookType: 'HighLimit' | 'HammerPlays' | 'Regular' | 'Props' (default 'Regular')
function getRoleId(bookName, webhookType = 'Regular') {
  const canonical = normalizeName(bookName);
  if (!canonical) return null;

  const isProps = webhookType?.toLowerCase() === 'props';
  const map = isProps ? propsRoles : mainlineRoles;

  return map[canonical] || null;
}

// Convenience: return Discord mention string or empty string if not found
function getMention(bookName, webhookType = 'Regular') {
  const id = getRoleId(bookName, webhookType);
  return id ? `<@&${id}>` : '';
}

module.exports = {
  mainlineRoles,
  propsRoles,
  aliasMap,
  normalizeName,
  getRoleId,
  getMention
};