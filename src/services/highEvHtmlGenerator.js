const moment = require('moment-timezone');
const config = require('../config');
const imageUrls = require('../config/imageUrls');
const puppeteer = require('puppeteer');
const logger = require('../utils/logger');

// Browser singleton management
let browserInstance = null;
let browserRefCount = 0;

async function getBrowser() {
  if (!browserInstance) {
    logger.debug('Creating new browser instance');
    
    // Updated browser launch configuration
    // On Mac, use bundled Chromium unless explicitly told to use system Chrome
    const useSystemChrome = process.env.USE_SYSTEM_CHROME === 'true' || 
                           (process.env.NODE_ENV === 'production' && process.platform === 'linux');
    
    const launchOptions = {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ],
      headless: 'new'
    };
    
    // On production/Linux, try to use system Chromium to avoid GLIBC issues
    if (useSystemChrome) {
      const chromiumPaths = [
        process.env.CHROMIUM_PATH,
        // Linux paths
        '/usr/bin/chromium-browser',
        '/usr/bin/chromium',
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        // Mac paths
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Chromium.app/Contents/MacOS/Chromium'
      ].filter(Boolean);
      
      // Also check for Nix store chromium
      const fs = require('fs');
      const glob = require('glob');
      try {
        const nixChromiums = glob.sync('/nix/store/*/bin/chromium');
        if (nixChromiums.length > 0) {
          chromiumPaths.unshift(nixChromiums[0]);
        }
      } catch (e) {
        // Ignore glob errors
      }
      
      let foundExecutable = false;
      for (const path of chromiumPaths) {
        try {
          if (fs.existsSync(path)) {
            launchOptions.executablePath = path;
            logger.debug(`Using system Chrome/Chromium at: ${path}`);
            foundExecutable = true;
            break;
          }
        } catch (e) {
          // Continue to next path
        }
      }
      
      if (!foundExecutable) {
        logger.warn('No system Chrome/Chromium found, falling back to bundled Chromium');
      }
    }
    
    browserInstance = await puppeteer.launch(launchOptions);
    
    // Handle graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, closing browser');
      if (browserInstance) {
        await browserInstance.close();
        browserInstance = null;
      }
    });
    
    process.on('SIGINT', async () => {
      logger.info('SIGINT received, closing browser');
      if (browserInstance) {
        await browserInstance.close();
        browserInstance = null;
      }
    });
  }
  
  browserRefCount++;
  return browserInstance;
}

async function releaseBrowser() {
  browserRefCount--;
  
  // Optional: Close browser if no active references after some time
  // This helps with memory but keeps browser alive during active periods
  if (browserRefCount === 0) {
    setTimeout(async () => {
      if (browserRefCount === 0 && browserInstance) {
        logger.debug('No active browser usage, closing browser instance');
        await browserInstance.close();
        browserInstance = null;
      }
    }, 300000); // 5 minutes
  }
}

function generateHighEvHtml(bets, pageNumber, totalPages) {
  logger.debug('Generating HTML for', bets.length, 'bets');

  // Sort bets by EV% in descending order
  bets.sort((a, b) => parseFloat(b.ev) - parseFloat(a.ev));

  const tableRows = bets.map((bet, index) => {
    try {
      logger.debug(`Processing bet ${index + 1}/${bets.length}: ${bet.book} - ${bet.game}`);
      const evValue = parseFloat(bet.ev) * 100; // Convert decimal to percentage
      const evColor = evValue >= 3 ? '#39ff14' : '#ff6f00';
      
      // Check if pinnacle_limits is NaN or invalid
      let pinnacleLimitDisplay;
      if (isNaN(bet.pinnacle_limits) || bet.pinnacle_limits === null || bet.pinnacle_limits === undefined) {
        // Show Circa logo when we don't have Pinnacle data
        pinnacleLimitDisplay = `<img src="${config.images['Circa'] || ''}" alt="Circa" style="width: 80px; height: 80px; border-radius: 50%; margin: 0 auto;">`;
      } else {
        pinnacleLimitDisplay = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(bet.pinnacle_limits);
      }

      // Update the date formatting here
      const formattedDate = moment(bet.timestamp)
        .tz('America/New_York')  // Convert to Eastern Time
        .format('ddd, MMM D, h:mm A z');  // Format as desired

      return `
        <tr class="border-b border-gray-800 hover:bg-gray-700">
            <td class="px-2 py-6 text-center">
              <img src="${config.images[bet.book] || ''}" alt="${bet.book}" class="site-image">
            </td>
            <td class="px-2 py-6 game-info-column">
              <div class="game-info-container">
                <div class="game-details">
                  <div class="game-teams">${bet.game}</div>
                  <div class="game-time">${formattedDate}</div>
                </div>
              </div>
            </td>
            <td class="px-4 py-6 bet-details-column">
              <div class="bet-details-container">
                <div class="bet-badges">
                  <span class="league-badge">${bet.league}</span>
                  <span class="market-badge">${bet.market_display.replace(/[()]/g, '')}</span>
                </div>
                <span class="bet-selection">${bet.selection}</span>
              </div>
            </td>
            <td class="px-1 py-6 odds-row">
              <div class="odds-box-bet">${bet.odds_american}</div>
            </td>
            <td class="px-1 py-6 odds-row">
              <div class="odds-box-bet">${bet.qk_display}</div>
            </td>
            <td class="px-2 py-6 ev-column">
              ${generateEvPctProgressBar(evValue, evColor)}
            </td>
            <td class="px-1 py-6 odds-row">
              <div class="odds-box-bet fair-odds-box">${bet.fv > 0 ? `+${bet.fv}` : bet.fv}</div>
            </td>
            <td class="px-1 py-6 odds-row">
              <div class="odds-box-bet pinnacle-limit-box">${pinnacleLimitDisplay}</div>
            </td>
        </tr>
      `;
    } catch (error) {
      logger.error('Error generating row for bet:', bet, error);
      return '';
    }
  }).join('');

  logger.debug('HTML table rows generated successfully');

  const pageIndicator = totalPages > 1 
    ? `<div class="page-indicator">${pageNumber}/${totalPages}</div>`
    : '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Edge Zone Optimizer</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@600&display=swap" rel="stylesheet">
        <style>
            :root {
              --accent-color: #13161c;
            }
            body { 
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
              font-weight: 600;
              background-color: #13161c; 
              color: #f7fafc; 
              font-size: 120px; 
            }
            .site-image {
              width: 140px;
              height: 140px;
              border-radius: 20%; /* Changed from 50% to 20% for rounded corners */
              object-fit: cover;
              display: block;
              margin: 0 auto;
              box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2); /* Added shadow for depth */
            }
            .game-info-column, .bet-details-column {
              text-align: center; /* Changed from 'left' to 'center' */
              vertical-align: middle;
            }
            .game-info-container {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center; /* Added to center-align content */
              height: 100%;
              padding: 36px 0;
            }
            .bet-details-container {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center; /* Added to center-align content */
              height: 100%;
              padding: 16px 0;
            }
            .bet-badges {
              display: flex;
              gap: 14px;
              margin-bottom: 14px;
            }
            .league-badge, .market-badge {
              background-color: #4a5568;
              color: white;
              font-size: 2.8rem;
              font-weight: 600;
              padding: 10px 14px;
              border-radius: 10px;
              white-space: nowrap;
            }
            .market-badge {
              background-color: #2d3748;
            }
            .game-teams {
              color: white;
              font-size: 3.6rem;
              margin-bottom: 14px;
              line-height: 1.2;
            }
            .bet-selection {
              color: white;
              font-size: 3.6rem;
              text-align: left;
              line-height: 1.2;
            }
            .game-details {
              display: flex;
              flex-direction: column;
            }
            .game-time {
              color: #a0aec0;
              font-size: 3.2rem;
            }
            .odds-box-bet {
              display: flex;
              justify-content: center;
              align-items: center;
              white-space: nowrap;
              background-color: #2d3748;
              border: none;
              border-radius: 16px;
              padding: 12px 16px;
              color: white;
              font-size: 3.2rem;
              font-weight: normal;
              min-width: 200px;
              max-width: 220px;
              margin: 0 auto;
              overflow: visible;
            }
            .pinnacle-limit-box {
              display: flex;        /* Allow the box to size based on its content */
              justify-content: center;
              align-items: center;
              white-space: nowrap;
              background-color: #0b1323;
              color: white;
              padding: 12px 16px;
              border-radius: 16px;
              font-size: 3.2rem;
              font-weight: normal;
              min-width: 325px;             /* Remove the inherited min-width */
              max-width: 340px;             /* Remove the inherited max-width */                 /* Let the width adjust based on content */
              margin: 0 auto;               /* Center the box horizontally */
              overflow: visible;
            }
            .fair-odds-box {
              background-color: #2d3748;
              color: white; /* Adjust text color for readability */
            }
            .data-container {
              max-width: 100vw;
              margin: auto;
              box-shadow: 0 14px 16px rgba(0,0,0,0.1);
              border-radius: 1.6rem;
              overflow: visible;
            }
            table {
              table-layout: fixed;
              width: 100%;
            }
            th, td {
              padding: 3rem 1.5rem;
              text-align: center;
              vertical-align: middle;
            }
            th {
              font-size: 3.6rem;
              font-weight: bold;
            }
            .ev-column {
              width: 280px;
              padding-top: 1.75rem;
              padding-bottom: 1.75rem;
            }
            .progress-bar-segment {
              height: 26px;
              width: 13px;
              clip-path: polygon(40% 0px, 100% 0px, 60% 100%, 0px 100%);
            }
            .provider-header-box {
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #0b1323;
              border-radius: 16px; /* Adjusted for larger size */
              padding: 20px 52px; /* Adjusted for larger size */
              width: fit-content;
              margin: 0 auto;
            }
            .provider-header-image {
              width: 90px; /* Adjusted for larger size */
              height: 90px; /* Adjusted for larger size */
              border-radius: 50%;
              margin-right: 24px;
              margin-left: -12px;
              margin-top: 4px;
            }
            .provider-header-text {
              color: #a0aec0;
              font-size: 3.6rem; /* Adjusted for larger size */
            }
            .page-indicator {
                position: absolute;
                top: 20px;
                right: 60px; 
                font-size: 3.6rem;
                color: #a0aec0;
                background-color: rgba(0, 0, 0, 0.5);
                padding: 12px 24px; /* Slightly increased padding for larger size */
                border-radius: 12px; /* Slightly increased border-radius for larger size */
            }
            .custom-header {
                padding-left: 60px; /* Add this line to create 50px padding on the left */
            }
        </style>
    </head>
    <body class="custom-bg text-gray-300 flex flex-col items-center justify-center">
        <div class="data-container max-w-full w-full my-6 relative">
            ${pageIndicator}
            <div class="custom-header flex justify-between items-center py-8">
                <div>
                    <img src="${imageUrls.edgeZone.logo}" alt="Edge Zone Logo" style="height: 100px; width: auto;">
                </div>
            </div>
            <table class="min-w-full leading-normal">
                <thead>
                    <tr class="custom-table-header text-gray-400">
                        <th class="w-36">Site</th>
                        <th class="w-1/5 text-center">Game Info</th>
                        <th class="w-1/5 text-center">Bet Details</th>
                        <th class="w-48">Odds</th>
                        <th class="w-48">QK</th>
                        <th class="ev-column">EV %</th>
                        <th class="w-48">Fair Odds</th>
                        <th class="w-56">
                          <div class="provider-header-box">
                            <img class="provider-header-image" src="${config.images['Pinnacle']}" alt="Pinnacle" />
                            <span class="provider-header-text">Limit</span>
                          </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
        </div>
    </body>
    </html>
  `;
}

function generateEvPctProgressBar(evPercentage, color) {
  const totalSegments = 18;
  let adjustedEvPercentage = Math.min(Math.max(evPercentage, 0), 10);
  let filledSegments = Math.round((adjustedEvPercentage / 10) * totalSegments);

  if (evPercentage <= 1) filledSegments = 1;
  if (evPercentage > 10) filledSegments = totalSegments;

  let segmentsHtml = "";

  for (let i = 0; i < totalSegments; i++) {
    const bgColor = i < filledSegments ? color : "#2D3748";
    segmentsHtml += `<div class="progress-bar-segment" style="background-color: ${bgColor};"></div>`;
  }

  return `
    <div class="text-center w-full flex flex-col items-center justify-between" style="height: 120px;">
        <div class="text-white text-6xl font-bold mb-1">${evPercentage.toFixed(2)}%</div>
        <div class="flex justify-center gap-[3px] w-full" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="${adjustedEvPercentage}" aria-valuemin="0" aria-valuemax="10">
            ${segmentsHtml}
        </div>
    </div>
  `;
}

async function generateHighEvImage(bets) {
  logger.debug('Starting to generate high EV image(s)');
  
  // Filter bets to only show games within 1 month
  const currentDate = moment();
  const oneMonthFromNow = moment().add(1, 'month');
  const filteredBets = bets.filter(bet => {
    const gameDate = moment(bet.timestamp);
    return gameDate.isBefore(oneMonthFromNow);
  });
  
  logger.debug(`Filtered from ${bets.length} to ${filteredBets.length} bets within 1 month`);
  
  const batchSize = 10;
  const imageBuffersWithTimestamps = [];
  const totalPages = Math.ceil(filteredBets.length / batchSize);

  for (let i = 0; i < filteredBets.length; i += batchSize) {
    const batchBets = filteredBets.slice(i, i + batchSize);
    const pageNumber = Math.floor(i / batchSize) + 1;
    logger.debug(`Generating image ${pageNumber}/${totalPages} for bets ${i + 1} to ${Math.min(i + batchSize, filteredBets.length)}`);
    
    let page = null;
    try {
      const html = generateHighEvHtml(batchBets, pageNumber, totalPages);
      logger.debug('HTML generated, getting browser instance');
      
      const browser = await getBrowser();
      
      logger.debug('Got browser instance, creating new page');
      page = await browser.newPage();
      logger.debug('Setting page content');
      await page.setContent(html);
      
      // Increase the viewport size and set a larger scale
      logger.debug('Setting viewport');
      await page.setViewport({ width: 5000, height: 2800, deviceScaleFactor: 2 });

      logger.debug('Taking screenshot');
      await page.waitForSelector('.data-container');
      const element = await page.$('.data-container');
      const boundingBox = await element.boundingBox();
      
      // Add padding to prevent cutoff at the bottom
      const paddedBox = {
        x: boundingBox.x,
        y: boundingBox.y,
        width: boundingBox.width,
        height: boundingBox.height + 20 // Add 20px padding at bottom
      };
      
      const screenshotBuffer = await page.screenshot({
        clip: paddedBox,
        fullPage: false,
        type: 'png' // Specify png type explicitly
      });
      logger.debug('Screenshot taken, type:', typeof screenshotBuffer, 'isBuffer:', Buffer.isBuffer(screenshotBuffer));
      
      const imageBuffer = Buffer.isBuffer(screenshotBuffer) ? screenshotBuffer : Buffer.from(screenshotBuffer);
      const timestamp = Math.floor(Date.now() / 1000); // Current Unix timestamp
      
      imageBuffersWithTimestamps.push({ imageBuffer, timestamp, pageNumber, totalPages });
      
    } catch (error) {
      logger.error(`Error generating image for page ${pageNumber}:`, error);
      // Continue with next batch even if this one fails
    } finally {
      if (page) {
        await page.close();
        logger.debug('Page closed');
      }
      await releaseBrowser();
    }
  }
  
  logger.debug(`Generated ${imageBuffersWithTimestamps.length} image(s) with timestamps`);
  return imageBuffersWithTimestamps;
}

module.exports = { generateHighEvHtml, generateHighEvImage };