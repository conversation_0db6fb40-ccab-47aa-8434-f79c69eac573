const echarts = require('echarts');
const { createCanvas } = require('canvas');
const { getDb } = require('../db/mongo');
const moment = require('moment-timezone');
const sharp = require('sharp');

// We still import formatOddsFromProb for axis labeling:
const { formatOddsFromProb } = require('../utils/probTicks');

//
// 1) Helper conversions for American ↔ Probability
//
function americanToProb(odds) {
  if (odds === 100) return 0.5;
  if (odds >= 100) {
    return 100 / (odds + 100);
  } else {
    return -odds / (-odds + 100);
  }
}

function probToAmerican(prob) {
  if (prob <= 0 || prob >= 1) return null;
  return prob > 0.5
    ? -Math.round(100 * prob / (1 - prob))
    : Math.round(100 * (1 - prob) / prob);
}

//
// OddsChartGenerator Class using ECharts
//
class OddsChartGenerator {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.width = 1200;
    this.height = 720;
    
    moment.tz.setDefault('America/New_York');
  }

  async generateOddsChart(doc) {
    try {
      const marketKey = `${doc.game}|${doc.market_type}|${doc.market}|${doc.selection}`;
      const historicalData = await this.getHistoricalOdds(marketKey);
      
      if (historicalData.pinnacle.length < 1 && historicalData.circa.length < 1) {
        console.log('[CHART] No historical data found for', marketKey);
        return null;
      }

      // Use latest timestamp from either source for cache key
      const pinnacleLastTime = historicalData.pinnacle.length > 0 ? 
        historicalData.pinnacle[historicalData.pinnacle.length - 1].timestamp.getTime() : 0;
      const circaLastTime = historicalData.circa.length > 0 ? 
        historicalData.circa[historicalData.circa.length - 1].timestamp.getTime() : 0;
      const lastTimestamp = Math.max(pinnacleLastTime, circaLastTime);
      const cacheKey = `${marketKey}_${lastTimestamp}`;
      
      if (this.cache.has(cacheKey)) {
        console.log('[CHART] Using cached chart for', doc.selection);
        return this.cache.get(cacheKey);
      }

      console.log(`[CHART] Found ${historicalData.pinnacle.length} Pinnacle and ${historicalData.circa.length} Circa data points for ${doc.selection}`);
      
      const optimizedBuffer = await this.generateEChart(historicalData, doc);
      
      if (optimizedBuffer) {
        const base64 = optimizedBuffer.toString('base64');
        this.cache.set(cacheKey, base64);
        this.cleanCache();
        return base64;
      }
      
      return null;
    } catch (error) {
      console.error('[CHART] Error generating chart:', error);
      return null;
    }
  }

  async generateEChart(historicalData, doc) {
    try {
      let pinnacleChartData = [];
      let circaChartData = [];
      let limitData = [];
      
      let earliestDataTime = null;

      // Process Pinnacle historical data
      historicalData.pinnacle.forEach(record => {
        const prob = americanToProb(parseInt(record.odds_american_our_side));
        const timestamp = moment(record.timestamp).tz('America/New_York').valueOf();
        pinnacleChartData.push([timestamp, prob]);

        if (earliestDataTime === null || timestamp < earliestDataTime) {
          earliestDataTime = timestamp;
        }
        
        if (record.limit !== undefined && record.limit !== null) {
          const currentLimitVal = Number(record.limit);
          if (limitData.length === 0 || limitData[limitData.length - 1][1] !== currentLimitVal) {
            limitData.push([timestamp, currentLimitVal]);
          }
        }
      });

      // Process Circa historical data
      historicalData.circa.forEach(record => {
        if (record.odds_american) {
          const prob = americanToProb(parseInt(record.odds_american));
          const timestamp = moment(record.timestamp).tz('America/New_York').valueOf();
          circaChartData.push([timestamp, prob]);

          if (earliestDataTime === null || timestamp < earliestDataTime) {
            earliestDataTime = timestamp;
          }
        }
      });

      const currentTime = moment().tz('America/New_York').valueOf();
      let xAxisEndTime = currentTime;

      // Add current Pinnacle odds if available and we have historical data or it's the sharp book
      if (doc.pinnacle_odds_american_our_side && 
          doc.pinnacle_odds_american_our_side !== 'NaN' &&
          !isNaN(doc.pinnacle_odds_american_our_side) &&
          (historicalData.pinnacle.length > 0 || doc.sharp_book_used === 'Pinnacle')) {
        const currentProb = americanToProb(parseInt(doc.pinnacle_odds_american_our_side));
        pinnacleChartData.push([currentTime, currentProb]);
      }
      
      // Add current Circa odds if available and we have historical data or it's the sharp book
      if (doc.circa_odds_american_our_side && 
          (historicalData.circa.length > 0 || doc.sharp_book_used === 'Circa')) {
        const currentProb = americanToProb(parseInt(doc.circa_odds_american_our_side));
        circaChartData.push([currentTime, currentProb]);
      }
      
      // Combine all data points to find max time
      const allDataPoints = [...pinnacleChartData, ...circaChartData];
      if (allDataPoints.length > 0) {
        const maxChartDataTime = Math.max(...allDataPoints.map(p => p[0]));
        xAxisEndTime = Math.max(maxChartDataTime, currentTime);
      }

      let xAxisStartTime = moment(xAxisEndTime).subtract(6, 'hours').valueOf();

      if (earliestDataTime !== null && earliestDataTime > xAxisStartTime) {
        xAxisStartTime = earliestDataTime;
      }
      pinnacleChartData = pinnacleChartData.filter(pt => pt[0] >= xAxisStartTime);
      circaChartData = circaChartData.filter(pt => pt[0] >= xAxisStartTime);
      limitData = limitData.filter(pt => pt[0] >= xAxisStartTime);

      // Check if we have enough data points
      const totalDataPoints = pinnacleChartData.length + circaChartData.length;
      if (totalDataPoints < 2 && !(totalDataPoints === 1 && limitData.length > 0)) {
        if (totalDataPoints < 1 || (totalDataPoints === 1 && limitData.length === 0)) {
            console.log(`[CHART] Insufficient data points (${pinnacleChartData.length} Pinnacle, ${circaChartData.length} Circa, ${limitData.length} limits) for ${doc.selection}`);
            return null;
        }
      }
      
      if (doc.pinnacle_limits !== undefined && doc.pinnacle_limits !== null && !isNaN(doc.pinnacle_limits)) {
        const currentDocLimitValue = Number(doc.pinnacle_limits);
        if (limitData.length === 0) {
          // Use the earliest data point from either series
          const allPoints = [...pinnacleChartData, ...circaChartData];
          const startTime = allPoints.length > 0 ? Math.max(Math.min(...allPoints.map(p => p[0])), xAxisStartTime) : xAxisStartTime;
          limitData.push([startTime, currentDocLimitValue]);
          if (xAxisEndTime > startTime) {
            limitData.push([xAxisEndTime, currentDocLimitValue]);
          }
        } else {
          const lastHistPointInLimitData = limitData[limitData.length - 1];
          if (lastHistPointInLimitData[0] < xAxisEndTime) {
            limitData.push([xAxisEndTime, currentDocLimitValue]);
          } else if (lastHistPointInLimitData[0] === xAxisEndTime) {
            lastHistPointInLimitData[1] = currentDocLimitValue;
          }
        }
      }

      if (limitData.length > 0) {
        const lastKnownLimitPoint = limitData[limitData.length - 1];
        if (lastKnownLimitPoint[0] < xAxisEndTime) {
          limitData.push([xAxisEndTime, lastKnownLimitPoint[1]]);
        }
      }
      
      // Combine all probability values for y-axis calculation
      const allProbs = [...pinnacleChartData.map(pt => pt[1]), ...circaChartData.map(pt => pt[1])];
      const minP = allProbs.length > 0 ? Math.min(...allProbs) : 0.45;
      const maxP = allProbs.length > 0 ? Math.max(...allProbs) : 0.55;

      const totalTicks = 4;
      const intervals = totalTicks - 1;
      
      let yAxisMinP = minP;
      let yAxisMaxP = maxP;
      if (yAxisMinP === yAxisMaxP) {
          yAxisMinP = Math.max(0, yAxisMinP - 0.025);
          yAxisMaxP = Math.min(1, yAxisMaxP + 0.025);
      }
      if (yAxisMinP > yAxisMaxP) {
          [yAxisMinP, yAxisMaxP] = [yAxisMaxP, yAxisMinP];
      }

      const axisInterval = (yAxisMaxP - yAxisMinP) / intervals;

      let limitMinY = 0;
      let limitMaxY = 1000;
      let limitIntervalY = 250;
      let limitAxisTicks = null;
      
      if (limitData.length > 0) {
        const numericLimits = limitData.map(pt => pt[1]).filter(val => !isNaN(val) && val > 0);
        const uniqueLimits = [...new Set(numericLimits)];
        
        if (uniqueLimits.length === 1) {
          const singleLimit = uniqueLimits[0];
          limitAxisTicks = [singleLimit];
          const target = singleLimit;
          const padding = Math.max(target * 0.05, 10);
          limitMinY = Math.max(0, target - padding);
          limitMaxY = target + padding;
          limitIntervalY = padding;
        } else if (uniqueLimits.length > 1) {
          const minLimit = Math.min(...uniqueLimits);
          const maxLimit = Math.max(...uniqueLimits);
          limitMinY = minLimit;
          limitMaxY = maxLimit;
          const limitRange = limitMaxY - limitMinY;
          limitIntervalY = limitRange > 0 ? limitRange / 3 : 1;
          limitAxisTicks = [limitMinY, limitMinY + limitIntervalY, limitMinY + 2 * limitIntervalY, limitMaxY];
        }
      }

      const DPR = 2;
      const canvas = createCanvas(this.width, this.height);
      const chart = echarts.init(canvas, null, {
        width: this.width,
        height: this.height,
        devicePixelRatio: DPR
      });

      const seriesArr = [];
      
      // Add Pinnacle series if data exists
      if (pinnacleChartData.length > 0) {
        seriesArr.push({
          name: 'Pinnacle',
          type: 'line',
          data: pinnacleChartData,
          smooth: 0.2,
          symbol: 'circle',
          symbolSize: 9,
          lineStyle: {
            width: 3,
            color: '#ff5501',
            shadowBlur: 5,
            shadowColor: '#ff5501'
          },
          itemStyle: {
            color: '#13161c',
            borderColor: '#ff5501',
            borderWidth: 2
          },
          emphasis: { scale: 1.3 },
          yAxisIndex: 0
        });
      }
      
      // Add Circa series if data exists
      if (circaChartData.length > 0) {
        seriesArr.push({
          name: 'Circa',
          type: 'line',
          data: circaChartData,
          smooth: 0.2,
          symbol: 'circle',
          symbolSize: 9,
          lineStyle: {
            width: 3,
            color: '#00b4d8',  // Bright blue for Circa
            shadowBlur: 5,
            shadowColor: '#00b4d8'
          },
          itemStyle: {
            color: '#13161c',
            borderColor: '#00b4d8',
            borderWidth: 2
          },
          emphasis: { scale: 1.3 },
          yAxisIndex: 0
        });
      }

      if (limitData.length > 0) {
        seriesArr.push({
          name: 'Limit',
          type: 'line',
          step: 'end',
          data: limitData,
          symbol: 'circle',
          symbolSize: 9,
          lineStyle: {
            width: 3,
            color: '#e2e8f0'
          },
          itemStyle: {
            color: '#e2e8f0',
            borderColor: '#e2e8f0',
            borderWidth: 2
          },
          emphasis: { scale: 1.3 },
          yAxisIndex: 1
        });
      }

      const option = {
        backgroundColor: '#13161c',
        animation: false,
        title: {
          text: doc.selection,
          subtext: doc.game,
          left: 'center',
          top: 15,
          itemGap: 8,
          textStyle: {
            color: '#f7fafc',
            fontSize: 27,
            fontWeight: '700',
            fontFamily: 'Inter, sans-serif'
          },
          subtextStyle: {
            color: '#a0aec0',
            fontSize: 16.875,
            fontWeight: '500',
            fontFamily: 'Inter, sans-serif'
          }
        },
        legend: {
          show: true,
          data: (() => {
            const legendItems = [];
            
            // Add Pinnacle if it has data
            if (pinnacleChartData.length > 0) {
              legendItems.push({
                name: 'Pinnacle', 
                itemStyle: {color: '#ff5501', shadowBlur: 5, shadowColor: '#ff5501'}
              });
            }
            
            // Add Circa if it has data
            if (circaChartData.length > 0) {
              legendItems.push({
                name: 'Circa', 
                itemStyle: {color: '#00b4d8', shadowBlur: 5, shadowColor: '#00b4d8'}
              });
            }
            
            // Add Limit if it has data
            if (limitData.length > 0) {
              legendItems.push({
                name: 'Limit', 
                itemStyle: {color: '#e2e8f0'}
              });
            }
            
            return legendItems;
          })(),
          top: 25,
          right: 40,
          itemWidth: 24,
          itemHeight: 3,
          itemGap: 15,
          textStyle: {
            color: '#a0aec0',
            fontSize: 18,
            fontWeight: '400',
            fontFamily: 'Inter, sans-serif'
          },
          icon: 'rect',
          orient: 'horizontal'
        },
        grid: {
          left: 70,
          right: limitData.length > 0 ? 80 : 50,
          bottom: 70,
          top: 100,
          containLabel: false
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          min: xAxisStartTime,
          max: xAxisEndTime,
          axisLine: { show: true, lineStyle: { color: '#4a5568', width: 1.5 } },
          axisTick: { show: false },
          axisLabel: {
            color: '#a0aec0',
            fontSize: 14,
            fontWeight: '600',
            fontFamily: 'Inter, sans-serif',
            formatter: value => moment(value).tz('America/New_York').format('h:mm a')
          },
          splitLine: { show: false }
        },
        yAxis: [
          {
            type: 'value',
            min: yAxisMinP,
            max: yAxisMaxP,
            interval: axisInterval,
            splitNumber: intervals,
            nice: false,
            inverse: true,
            position: 'left',
            name: 'Odds',
            nameLocation: 'middle',
            nameGap: 45,
            nameTextStyle: { color: '#a0aec0', fontSize: 17, fontWeight: '600', fontFamily: 'Inter, sans-serif' },
            axisLine: { show: true, lineStyle: { color: '#4a5568', width: 1.5 } },
            axisTick: { show: false },
            axisLabel: {
              color: '#a0aec0',
              fontSize: 14,
              fontWeight: '600',
              fontFamily: 'Inter, sans-serif',
              formatter: val => formatOddsFromProb(val)
            },
            splitLine: { lineStyle: { color: '#2d3748', width: 1 } },
            minorSplitLine: { show: true, lineStyle: { color: '#1a202c', width: 1, type: 'dotted' } }
          },
          {
            type: 'value',
            min: limitMinY,
            max: limitMaxY,
            interval: limitIntervalY,
            splitNumber: limitAxisTicks && limitAxisTicks.length === 1 ? 2 : 3,
            position: 'right',
            name: 'Limits',
            nameLocation: 'middle',
            nameGap: 55,
            nameTextStyle: { color: '#e2e8f0', fontSize: 17, fontWeight: '600', fontFamily: 'Inter, sans-serif' },
            axisLine: { show: true, lineStyle: { color: '#4a5568', width: 1.5 } },
            axisTick: { show: false },
            axisLabel: {
              color: '#e2e8f0',
              fontSize: 14,
              fontWeight: '600',
              fontFamily: 'Inter, sans-serif',
              formatter: (value) => {
                if (limitAxisTicks && limitAxisTicks.length === 1) {
                  const target = limitAxisTicks[0];
                  if (Math.abs(value - target) < 1e-6) {
                    if (target >= 10000) return `${(target / 1000).toFixed(0)}k`;
                    if (target >= 1000) {
                      return target % 1000 === 0 
                        ? `${(target / 1000).toFixed(0)}k`
                        : `${(target / 1000).toFixed(1)}k`.replace('.0','');
                    }
                    return `${Math.round(target)}`;
                  }
                  return '';
                }
                
                const rounded = Math.round(value);
                if (rounded >= 10000) return `${(rounded / 1000).toFixed(0)}k`;
                if (rounded >= 1000 && rounded % 1000 === 0) return `${(rounded / 1000).toFixed(0)}k`;
                if (rounded >= 1000) return `${(rounded / 1000).toFixed(1)}k`.replace('.0','');
                return `${rounded}`;
              }
            },
            splitLine: { show: false }
          }
        ],
        series: seriesArr
      };
      
      if (limitData.length === 0) {
        option.yAxis.pop();
        option.grid.right = 50;
      }

      chart.setOption(option);
      const imageBuffer = canvas.toBuffer('image/png');
      chart.dispose();

      const optimizedBuffer = await sharp(imageBuffer)
        .png({ quality: 100, compressionLevel: 6 })
        .toBuffer();

      return optimizedBuffer;
    } catch (error) {
      console.error('[CHART] Error generating ECharts chart:', error);
      return null;
    }
  }

  async getHistoricalOdds(marketKey) {
    try {
      const db = await getDb();
      const cutoffTime = moment().subtract(6, 'hours').toDate();
      
      // Fetch both Pinnacle and Circa odds history
      const pinnacleCollection = db.collection('pinnacle_odds_history');
      const circaCollection = db.collection('circa_odds_history');
      
      const [pinnacleRecords, circaRecords] = await Promise.all([
        pinnacleCollection.find({
          market_key: marketKey,
          timestamp: { $gte: cutoffTime }
        }).sort({ timestamp: 1 }).toArray(),
        
        circaCollection.find({
          market_key: marketKey,
          timestamp: { $gte: cutoffTime }
        }).sort({ timestamp: 1 }).toArray()
      ]);
      
      console.log(`[CHART] Found ${pinnacleRecords.length} Pinnacle and ${circaRecords.length} Circa historical records for ${marketKey.substring(0, 50)}...`);
      
      return {
        pinnacle: pinnacleRecords,
        circa: circaRecords
      };
    } catch (error) {
      console.error('[CHART] Error fetching historical odds:', error);
      return { pinnacle: [], circa: [] };
    }
  }

  cleanCache() {
    if (this.cache.size > 50) {
      const sortedKeys = Array.from(this.cache.keys()).sort((a, b) => {
        const timeA = parseInt(a.split('_').pop());
        const timeB = parseInt(b.split('_').pop());
        return timeA - timeB;
      });
      const toRemove = sortedKeys.slice(0, this.cache.size - 40);
      toRemove.forEach(key => this.cache.delete(key));
    }
  }
}

const chartGenerator = new OddsChartGenerator();
module.exports = { chartGenerator };