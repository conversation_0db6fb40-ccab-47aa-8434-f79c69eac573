const { getDb } = require('../db/mongo');

class GroupedBetsTracker {
  constructor() {
    this.db = null;
    this.collection = null;
  }

  async init() {
    this.db = await getDb();
    this.collection = this.db.collection('edgezone_grouped_bets');
    await this.collection.createIndex({ group_key: 1 }, { unique: true });
  }

  async shouldSendGroup(groupKey, bets) {
    const doc = await this.collection.findOne({ group_key: groupKey });
    // Logic to determine if the group should be sent
    // For simplicity, send if not sent before
    return !doc;
  }

  async markGroupAsSent(groupKey, bets) {
    await this.collection.updateOne(
      { group_key: groupKey },
      { 
        $set: { 
          group_key: groupKey,
          sent_at: new Date(),
          bet_ids: bets.map(bet => bet.projection_id)
        }
      },
      { upsert: true }
    );
  }
}

module.exports = new GroupedBetsTracker();