// File: src/services/sentBetsTracker.js
const { getDb } = require('../db/mongo');
const moment = require('moment-timezone');

class SentBetsTracker {
  constructor() {
    this.db = null;
    this.collection = null;
    this.highEvCollection = null;
  }

  async init() {
    this.db = await getDb();
    this.collection = this.db.collection('edgezone_sent_bets');
    this.highEvCollection = this.db.collection('edgezone_high_ev_bets');
    await this.collection.createIndex({ projection_id: 1 }, { unique: true });
    await this.collection.createIndex({ group_key: 1 });
    await this.highEvCollection.createIndex({ projection_id: 1 }, { unique: true });
  }

  async wasSent(projectionId) {
    const doc = await this.collection.findOne({ projection_id: projectionId });
    if (doc) {
      const sixHoursAgo = moment().subtract(6, 'hours').toDate();
      return !doc.cleared && doc.last_sent > sixHoursAgo;
    }
    return false;
  }

  async wasSentBefore(projectionId) {
    const doc = await this.collection.findOne({ projection_id: projectionId });
    return doc !== null;
  }

  async shouldSend(projectionId, currentEv) {
    const doc = await this.collection.findOne({ projection_id: projectionId });
    if (!doc) return true;
    // Only send if the EV has improved by at least 4%
    const evDifference = currentEv - doc.last_ev;
    return evDifference >= 0.04;
  }

  async markAsSent(projectionId, ev, pinnacleOddsOurSide, pinnacleOddsOppositeSide, groupKey = null) {
    await this.collection.updateOne(
      { projection_id: projectionId },
      { 
        $set: { 
          last_ev: ev, 
          last_sent: new Date(),
          last_pinnacle_odds_american_our_side: pinnacleOddsOurSide,
          last_pinnacle_odds_american_opposite_side: pinnacleOddsOppositeSide,
          group_key: groupKey,
          cleared: false // Reset the cleared flag when marking as sent
        }
      },
      { upsert: true }
    );
  }

  async getLastSentData(projectionId) {
    const doc = await this.collection.findOne({ projection_id: projectionId });
    return doc ? {
      last_ev: doc.last_ev,
      last_pinnacle_odds_american_our_side: doc.last_pinnacle_odds_american_our_side,
      last_pinnacle_odds_american_opposite_side: doc.last_pinnacle_odds_american_opposite_side
    } : null;
  }

  // New method for grouped bets
  async shouldSendGroup(groupKey, bets) {
    const docs = await this.collection.find({ group_key: groupKey }).toArray();
    if (docs.length !== bets.length) return true;
    return bets.some((bet, index) => Math.abs(bet.ev - docs[index].last_ev) >= 0.04);
  }

  async updateHighEvBet(projectionId, ev) {
    await this.highEvCollection.updateOne(
      { projection_id: projectionId },
      { 
        $set: { 
          ev: ev, 
          last_updated: new Date()
        }
      },
      { upsert: true }
    );
  }

  async removeInactiveBets(activeBetIds) {
    await this.highEvCollection.deleteMany({
      projection_id: { $nin: activeBetIds }
    });
  }

  async getActiveHighEvBets() {
    return this.highEvCollection.find({}).toArray();
  }

  async updateHighEvBets(currentHighEvBets) {
    console.log('updateHighEvBets: currentHighEvBets count:', currentHighEvBets.length);
    const currentIds = currentHighEvBets.map(bet => bet.projection_id);
    const existingBets = await this.highEvCollection.find({}).toArray();
    console.log('updateHighEvBets: existingBets count:', existingBets.length);
    const existingIds = existingBets.map(bet => bet.projection_id);

    let changes = false;

    // Detect new bets
    const newBets = currentHighEvBets.filter(bet => !existingIds.includes(bet.projection_id));
    console.log('updateHighEvBets: newBets count:', newBets.length);
    if (newBets.length > 0) {
      changes = true;
      await Promise.all(newBets.map(bet => this.updateHighEvBet(bet.projection_id, bet.ev)));
    }

    // Detect if any existing bet has an EV change of at least 0.04
    for (const bet of currentHighEvBets) {
      const existingBet = existingBets.find(b => b.projection_id === bet.projection_id);
      if (existingBet) {
        const evDiff = Math.abs(bet.ev - existingBet.ev);
        if (evDiff >= 0.04) {
          console.log(`updateHighEvBets: EV change detected for ${bet.projection_id}: ${existingBet.ev} -> ${bet.ev}`);
          changes = true;
          await this.updateHighEvBet(bet.projection_id, bet.ev);
        }
      }
    }

    // Detect removed bets
    const removedBets = existingBets.filter(bet => !currentIds.includes(bet.projection_id));
    console.log('updateHighEvBets: removedBets count:', removedBets.length);
    if (removedBets.length > 0) {
      changes = true;
      await Promise.all(removedBets.map(bet => this.highEvCollection.deleteOne({ projection_id: bet.projection_id })));
    }

    console.log('updateHighEvBets: changes detected =', changes);
    return changes;
  }

  async clearOldBets() {
    const sixHoursAgo = moment().subtract(6, 'hours').toDate();
    const result = await this.collection.updateMany(
      { last_sent: { $lt: sixHoursAgo } },
      { $set: { cleared: true } }
    );
    console.log(`Marked ${result.modifiedCount} old bets as cleared in edgezone_sent_bets collection`);
  }

  async getLastHighEvSendTime() {
    const doc = await this.db.collection('edgezone_system_state').findOne({ _id: 'high_ev_last_sent' });
    return doc ? doc.timestamp : null;
  }

  async updateLastHighEvSendTime() {
    await this.db.collection('edgezone_system_state').updateOne(
      { _id: 'high_ev_last_sent' },
      { $set: { timestamp: new Date() } },
      { upsert: true }
    );
  }
}

module.exports = new SentBetsTracker();
