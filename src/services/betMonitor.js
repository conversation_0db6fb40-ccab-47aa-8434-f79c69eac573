// File: src/services/betMonitor.js
const { getDb } = require('../db/mongo');
const { createEmbed, createGroupedEmbed, sendWebhook, sendHighEvWebhook, createModelEmbed, createConsolidatedModelEmbed } = require('../discord/webhook');
const config = require('../config');
const moment = require('moment-timezone');
const { generateHighEvImage } = require('./highEvHtmlGenerator');
const sentBetsTracker = require('./sentBetsTracker');
const logger = require('../utils/logger');

async function checkCollection(db, collectionName) {
  await sentBetsTracker.clearOldBets();

  const collection = db.collection(collectionName);
  const currentTime = moment().toDate(); // Convert to Date object
  
  logger.debug(`Checking collection ${collectionName} at ${currentTime}`);
  
  const docCount = await collection.countDocuments({
    $and: [
      { ev: { $gte: 0.02 } },
      { qk: { $gt: 0.1 } },
      { timestamp: { $gt: currentTime } }
    ]
  });

  logger.debug(`Found ${docCount} documents in collection ${collectionName}`);

  const cursor = collection.find({ 
    $and: [
      { ev: { $gte: 0.02 } },
      { qk: { $gt: 0.1 } },
      { timestamp: { $gt: currentTime } }
    ]
  });

  let embedsSent = 0;
  let highLimitEmbedsSent = 0;
  let hammerPlaysSent = 0;

  const groupedBets = {};

  for await (const doc of cursor) {
    try {
      // CRITICAL CHANGE: Check if the bet was FULLY sent (with longer timeout)
      // This prevents multiple clients from sending the exact same bet to ALL webhooks
      const wasSent = await sentBetsTracker.wasSent(doc.projection_id);
      if (wasSent) {
        logger.debug(`Bet ${doc.projection_id} was already sent within timeout period, skipping`);
        continue;
      }
      
      // For bets that haven't been fully sent yet, check if we should send it (based on EV change)
      const shouldSend = await sentBetsTracker.shouldSend(doc.projection_id, doc.ev);
      const wasSentBefore = await sentBetsTracker.wasSentBefore(doc.projection_id);
      
      if (shouldSend) {
        const groupKey = `${doc.game}_${doc.market_type}`;
        if (!groupedBets[groupKey]) {
          groupedBets[groupKey] = [];
        }
        groupedBets[groupKey].push({ ...doc, wasSentBefore });
      }
    } catch (error) {
      logger.error(`Error processing document in ${collectionName}:`, error);
    }
  }

  for (const [groupKey, bets] of Object.entries(groupedBets)) {
    const shouldSendGroup = await sentBetsTracker.shouldSendGroup(groupKey, bets);
    if (shouldSendGroup) {
      let embedContent;
      if (bets.length > 1) {
        embedContent = await createGroupedEmbed(bets);
      } else {
        const lastSentData = await sentBetsTracker.getLastSentData(bets[0].projection_id);
        embedContent = await createEmbed(bets[0], lastSentData);
      }

      // Add disclaimer if any bet in the group was sent before
      const anySentBefore = bets.some(bet => bet.wasSentBefore);
      if (anySentBefore) {
        embedContent.embed.addFields({
          name: 'Note',
          value: '🚨 This bet was sent before'
        });
      }

      const highestLimitBet = bets.reduce((max, bet) => bet.pinnacle_limits > max.pinnacle_limits ? bet : max, bets[0]);
      const highestQkBet = bets.reduce((max, bet) => bet.qk > max.qk ? bet : max, bets[0]);
      const highestEvBet = bets.reduce((max, bet) => bet.ev > max.ev ? bet : max, bets[0]);

      // Log which bets are being processed
      const projectionIds = bets.map(bet => bet.projection_id).join(', ');
      logger.debug(`Processing bets for group ${groupKey} with projection IDs: ${projectionIds}, Highest EV: ${highestEvBet.ev}`);

      // Step 2: Send to high limit webhook if limits >= 1500 AND EV >= 3%
      if (highestLimitBet.pinnacle_limits >= 1500 && highestLimitBet.ev >= 0.03) {
        logger.debug(`High Limit condition met: Limit=${highestLimitBet.pinnacle_limits}, EV=${highestLimitBet.ev}`);
        await sendWebhook('HighLimit', embedContent, highestLimitBet.book);
        highLimitEmbedsSent++;
      }

      // Step 3: Send to regular webhook if highest EV in group is >= 2%
      if (highestEvBet.ev >= 0.01) {
        logger.debug(`Regular Channel condition met: EV=${highestEvBet.ev}`);
        await sendWebhook(collectionName, embedContent, highestEvBet.book);
        embedsSent++;
      } else {
          logger.debug(`Regular Channel condition NOT met: EV=${highestEvBet.ev} < 0`);
      }

      // Step 2: Send hammer plays if highest EV >= 3% and QK >= 2
      if (highestEvBet.ev >= 0.03 && highestQkBet.qk >= 2) {
        logger.debug(`Hammer Plays condition met: EV=${highestEvBet.ev}, QK=${highestQkBet.qk}`);
        await sendWebhook('HammerPlays', embedContent, highestQkBet.book);
        hammerPlaysSent++;
      }

      // KEEP this at the end - only mark bets as sent AFTER all webhooks have been processed
      // This ensures each client can send to its own Discord webhooks
      for (const bet of bets) {
        await sentBetsTracker.markAsSent(
          bet.projection_id, 
          bet.ev, 
          bet.pinnacle_odds_american_our_side, 
          bet.pinnacle_odds_american_opposite_side,
          groupKey
        );
      }
    }
  }

  return { embedsSent, highLimitEmbedsSent, hammerPlaysSent, docCount };
}

async function checkHighEvBets(db) {
  const highEvBets = [];
  const currentTime = moment().toDate();

  for (const collectionName of config.collections) {
    const collection = db.collection(collectionName);
    const cursor = collection.find({
      $and: [
        { ev: { $gte: 0.01 } },
        { timestamp: { $gt: currentTime } }
      ]
    });

    for await (const doc of cursor) {
      highEvBets.push(doc);
    }
  }

  // Sort highEvBets by EV in descending order
  highEvBets.sort((a, b) => parseFloat(b.ev) - parseFloat(a.ev));

  logger.debug(`[checkHighEvBets] Found ${highEvBets.length} bets >= 2% EV for potential HTML generation.`);

  const hasChanges = await sentBetsTracker.updateHighEvBets(highEvBets);

  // Check if we should force send (e.g., first run or periodic update)
  const lastSendTime = await sentBetsTracker.getLastHighEvSendTime();
  const hoursSinceLastSend = lastSendTime ? (Date.now() - lastSendTime) / (1000 * 60 * 60) : 999;
  const shouldForceSend = hoursSinceLastSend > 6; // Force send every 6 hours

  if (hasChanges || shouldForceSend) {
    if (hasChanges) {
      logger.info('[checkHighEvBets] Changes detected, triggering High EV webhook send.');
    } else {
      logger.info(`[checkHighEvBets] Force sending High EV webhook (${hoursSinceLastSend.toFixed(1)} hours since last send.`);
    }
    await sendHighEvWebhook(highEvBets);
    await sentBetsTracker.updateLastHighEvSendTime();
  } else {
    logger.debug('[checkHighEvBets] No significant changes detected, skipping High EV webhook send.');
  }

  return highEvBets.length;
}

async function checkModelBets(db) {
  const currentTime = moment().toDate();
  let modelEmbedsSent = 0;

  // Query the dedicated model_edges collection directly
  const collection = db.collection('model_edges');
  const cursor = collection.find({
    $and: [
      { timestamp: { $gt: currentTime } },
      { edge_points: { $gte: 3, $lt: 10 } },  // Between 3 and 10
      { ev: { $gte: 5, $lt: 70 } }           // Between 5% and 70%
    ]
  });

  for await (const doc of cursor) {
    try {
      const modelKey = `model:${doc.projection_id}`;
      const wasModelSent = await sentBetsTracker.wasSent(modelKey);
      if (wasModelSent) continue;

      const embedContent = await createModelEmbed(doc);
      await sendWebhook('Model', embedContent, doc.book);
      await sentBetsTracker.markAsSent(modelKey, typeof doc.ev === 'number' ? doc.ev : null, null, null, 'model');
      modelEmbedsSent++;
    } catch (err) {
      logger.error(`Error processing model doc:`, err);
    }
  }

  return modelEmbedsSent;
}

async function monitorCollections() {
  const db = await getDb();
  const summaryLog = {};
  let totalHighLimitEmbeds = 0;
  let totalHammerPlays = 0;
  
  for (const collection of config.collections) {
    const { embedsSent, highLimitEmbedsSent, hammerPlaysSent, docCount } = await checkCollection(db, collection);
    summaryLog[collection] = { embedsSent, docCount };
    totalHighLimitEmbeds += highLimitEmbedsSent;
    totalHammerPlays += hammerPlaysSent;
  }

  const totalHighEvBets = await checkHighEvBets(db);

  logger.info('Job run summary:');
  for (const [collection, summary] of Object.entries(summaryLog)) {
    logger.info(`${collection}: ${summary.embedsSent} embeds sent, ${summary.docCount} documents found`);
  }
  logger.info('Total regular embeds sent:', Object.values(summaryLog).reduce((a, b) => a + b.embedsSent, 0));
  logger.info('Total high limit embeds sent:', totalHighLimitEmbeds);
  logger.info('Total Hammer Plays sent:', totalHammerPlays);
  logger.info('Total High EV bets found:', totalHighEvBets);
  logger.info('---');
}

async function runModelBatch() {
  const db = await getDb();
  const collection = db.collection('model_edges');

  // Time window: from run-time to 7 days ahead (ET)
  const startEt = moment().tz('America/New_York');
  const endEt = startEt.clone().add(7, 'days');

  const docs = await collection.find({
    timestamp: { $gte: startEt.toDate(), $lt: endEt.toDate() }
  }).toArray();

  // Group by (league, game, market_type, selection, book_line, rounded game time)
  const groups = new Map();
  for (const d of docs) {
    const key = [
      d.league || '',
      d.game || '',
      d.market_type || d.market || '',
      d.selection || '',
      d.book_line !== undefined ? d.book_line : 'ML',
      moment(d.timestamp).tz('America/New_York').format('YYYY-MM-DD HH:mm')
    ].join('|');
    if (!groups.has(key)) groups.set(key, []);
    groups.get(key).push(d);
  }

  let sent = 0;
  for (const [, groupDocs] of groups.entries()) {
    try {
      const embedContent = await createConsolidatedModelEmbed(groupDocs);
      await sendWebhook('Model', embedContent, null);
      sent++;
    } catch (err) {
      logger.error('[runModelBatch] Error sending consolidated model embed:', err);
    }
  }

  logger.info(`[runModelBatch] Sent ${sent} consolidated model embeds for window ${startEt.format()} -> ${endEt.format()}`);
}

async function initializeMonitoring() {
  await sentBetsTracker.init();
  return monitorCollections;
}

module.exports = { initializeMonitoring, runModelBatch };
