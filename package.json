{"name": "edgezone-gamelines", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js"}, "keywords": ["edgezone", "gamelines", "betting", "monitoring"], "author": "Edge Zone", "license": "ISC", "description": "Edge Zone Gamelines Monitoring System", "dependencies": {"canvas": "^3.1.0", "chart.js": "^3.9.1", "chartjs-adapter-moment": "^1.0.1", "chartjs-node-canvas": "^4.1.6", "chartjs-plugin-datalabels": "^2.2.0", "discord.js": "^14.15.3", "dotenv": "^16.4.5", "echarts": "^5.6.0", "form-data": "^4.0.0", "glob": "^10.3.10", "moment-timezone": "^0.5.45", "mongodb": "^6.8.0", "node-schedule": "^2.1.1", "puppeteer": "^23.5.1", "sharp": "^0.33.5"}}