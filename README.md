# Edge Zone Gamelines

Edge Zone Gamelines Monitoring System - A real-time monitoring system for sports betting lines and high EV opportunities.

## Features

- Real-time monitoring of multiple sportsbooks
- High EV bet detection and alerts
- Discord webhook notifications
- Automated chart generation for odds movements
- Grouped bet detection for correlated opportunities

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Fill in your MongoDB URI and Discord webhook URLs
   - Add image URLs for sportsbook logos and Edge Zone branding

3. **Start the application:**
   ```bash
   npm start
   ```

## Docker Deployment

Build and run with Docker:
```bash
docker build -t edgezone-gamelines .
docker run -d --env-file .env edgezone-gamelines
```

## Supported Sportsbooks

- FanDuel
- DraftKings
- Fliff
- Hard Rock
- Rebet
- Bovada
- Fanatics
- NoVig
- ProphetX
- Underdog
- Caesars
- BetOnline

## Configuration

The system uses environment variables for all configuration. See `.env.example` for all available options.

## License

ISC License