// Test the new filter criteria
const { MongoClient } = require('mongodb');
const moment = require('moment-timezone');
require('dotenv').config();

async function testNewFilter() {
  const client = new MongoClient(process.env.MONGO_URI);
  
  try {
    await client.connect();
    const dbName = process.env.DB_NAME || 'edgezone_game_lines_dev';
    const db = client.db(dbName);
    const currentTime = moment().toDate();
    
    console.log('Testing new filter: edge_points >= 3 AND ev >= 5');
    console.log('Current time:', currentTime);
    console.log('---\n');
    
    const collection = db.collection('model_edges');
    
    // Count with new filter
    const matchingCount = await collection.countDocuments({
      $and: [
        { timestamp: { $gt: currentTime } },
        { edge_points: { $gte: 3 } },
        { ev: { $gte: 5 } }
      ]
    });
    
    console.log(`Bets matching new filter: ${matchingCount}`);
    
    // Show some samples
    const samples = await collection.find({
      $and: [
        { timestamp: { $gt: currentTime } },
        { edge_points: { $gte: 3 } },
        { ev: { $gte: 5 } }
      ]
    }).limit(5).toArray();
    
    console.log('\nSample bets that will be sent:');
    samples.forEach(doc => {
      console.log(`  - ${doc.game}: edge=${doc.edge_points}, ev=${doc.ev}%, book=${doc.book}`);
    });
    
    // Check the specific Colorado @ Georgia Tech bet
    const specificBet = await collection.findOne({
      projection_id: "model_ncaaf_fliff_ncaaf|20250830|colorado|georgia tech_full_game_spread_home_4.5"
    });
    
    if (specificBet) {
      console.log('\n---');
      console.log('Colorado @ Georgia Tech bet:');
      console.log(`  Edge points: ${specificBet.edge_points} (>=3? ${specificBet.edge_points >= 3})`);
      console.log(`  EV: ${specificBet.ev}% (>=5? ${specificBet.ev >= 5})`);
      console.log(`  Will be sent: ${specificBet.edge_points >= 3 && specificBet.ev >= 5}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

testNewFilter();