// Test script to send a sample model embed to the webhook
const { WebhookClient, EmbedBuilder } = require('discord.js');
const moment = require('moment-timezone');
const config = require('./src/config');
const { normalizeName } = require('./src/config/discordRoles');

// Sample MongoDB document from model_edges collection
const doc = {
  _id: { $oid: "68ae5b5e5e33f130bbc2f13f" },
  projection_id: "model_ncaaf_fliff_ncaaf|20250830|purdue|ball state_full_game_spread_away_18.5",
  book: "fliff",
  book_decimal: 1.87,
  book_line: 18.5,
  book_price: -114,
  breakeven_prob: 0.5327102803738317,
  created_at: "2025-08-27T01:17:17.687215+00:00",
  edge_points: 4.5,
  ev: 32.14,
  ev_display: "32.14%",
  ev_type: "diff_percent",
  game: "Ball State @ Purdue",
  league: "NCAAF",
  market: "spread",
  market_type: "GAMESPREAD",
  model_fair_line: 14,
  odds_american: -114,
  selection: "away",
  source: "ftntools-model",
  timestamp: new Date("2025-08-30T16:00:00.000Z")
};

// Create the model embed
function createModelEmbed(doc) {
  const canonicalBook = normalizeName(doc.book) || doc.book;
  const authorIcon = config.images[canonicalBook] || config.images.edgezoneThumbnail;

  // Derive market label from doc.market
  const marketLabel = doc.market
    ? (doc.market.toLowerCase() === 'spread' ? 'Spread'
      : doc.market.toLowerCase() === 'moneyline' ? 'Moneyline'
      : doc.market.toLowerCase() === 'total' ? 'Total'
      : doc.market)
    : (doc.market_type || '');

  // Format selection - capitalize if it's a side like "away" or "home"
  const formattedSelection = doc.selection
    ? doc.selection.charAt(0).toUpperCase() + doc.selection.slice(1).toLowerCase()
    : '';

  // Add proper sign to lines
  const formatLine = (line) => {
    if (line == null) return null;
    const numLine = parseFloat(line);
    return numLine > 0 ? `+${line}` : `${line}`;
  };

  // Format timestamp
  const ts = doc.timestamp ? moment(doc.timestamp).tz('America/New_York').format('ddd, MMM D, h:mm A z') : '';
  
  // Build title - include the line with proper sign
  const formattedBookLine = formatLine(doc.book_line);
  const title = `${doc.league || ''} - ${formattedSelection} ${marketLabel || ''} ${formattedBookLine || ''}`.replace(/\s+/g, ' ').trim();

  // Build description
  const description = `${doc.game || ''}${ts ? ' - ' + ts : ''}`;

  // Build the detailed field with model-specific information
  const fields = [{
    name: `${canonicalBook} Model`,
    value: [
      doc.odds_american != null ? `Bet Odds: ${doc.odds_american}` : (doc.book_price != null ? `Bet Odds: ${doc.book_price}` : null),
      doc.book_line != null ? `Book Line: ${formatLine(doc.book_line)}` : null,
      doc.model_fair_line != null ? `Model Line: ${formatLine(doc.model_fair_line)}` : null,
      doc.ev_display ? `Diff %: ${doc.ev_display}` : null,
      doc.edge_points != null ? `Edge Pts: ${doc.edge_points}` : null
    ].filter(Boolean).join('\n')
  }];

  const embed = new EmbedBuilder()
    .setColor('#00b894') // Distinct color for model embeds
    .setTitle(title)
    .setDescription(description)
    .setAuthor({ name: canonicalBook, iconURL: authorIcon })
    .setThumbnail(config.images.edgezoneThumbnail)
    .addFields(...fields)
    .setFooter({ text: 'Powered by Edge Zone', iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setTimestamp();

  return { embed };
}

// Send the webhook
async function sendTestWebhook() {
  try {
    const webhookUrl = 'https://discord.com/api/webhooks/1409997143485321308/h_57oDgCaSwFtNk6zLSsz0Vn2JmIY-WdO_2x80crMvfomCApacSZ-D4bosw6mBHnTccc';
    const webhookClient = new WebhookClient({ url: webhookUrl });
    
    const embedContent = createModelEmbed(doc);
    
    // Create mobile notification content
    function createMobileNotificationContent(embed) {
      const title = embed.data?.title || '';
      let evInfo = '';
      let oddsInfo = '';
      
      if (embed.data?.fields) {
        // Try to find Diff % 
        for (const f of embed.data.fields) {
          const diffMatch = f.value && f.value.match(/Diff %:\s*([^\n]+)/i);
          if (diffMatch) {
            evInfo = diffMatch[1].trim();
          }
          const oddsMatch = f.value && f.value.match(/Bet Odds:\s*([^\n]+)/i);
          if (oddsMatch) {
            oddsInfo = oddsMatch[1].trim();
          }
        }
      }
      
      let notificationText = title;
      if (oddsInfo) {
        const titleParts = title.split(' - ');
        if (titleParts.length >= 2) {
          const selectionAndMarket = titleParts[1];
          const selectionMatch = selectionAndMarket.match(/^([^(]+)/);
          const selection = selectionMatch ? selectionMatch[1].trim() : selectionAndMarket;
          notificationText = `${titleParts[0]} - ${selection} (${oddsInfo})`;
        }
      }
      
      if (evInfo) {
        notificationText += ` • Diff: ${evInfo}`;
      }
      
      return notificationText;
    }
    
    const mobileContent = createMobileNotificationContent(embedContent.embed);
    
    console.log('Sending model embed to webhook...');
    console.log('Title:', embedContent.embed.data.title);
    console.log('Description:', embedContent.embed.data.description);
    console.log('Field:', embedContent.embed.data.fields[0]);
    console.log('Mobile notification:', mobileContent);
    
    await webhookClient.send({
      content: mobileContent,
      embeds: [embedContent.embed]
    });
    
    console.log('✅ Model webhook sent successfully!');
    console.log('Check the Discord channel to see the embed.');
    
  } catch (error) {
    console.error('❌ Error sending webhook:', error);
  }
}

// Run the test
sendTestWebhook();