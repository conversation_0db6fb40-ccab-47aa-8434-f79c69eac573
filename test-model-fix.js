// Test the fixed checkModelBets function
const { getDb } = require('./src/db/mongo');
const { createModelEmbed, sendWebhook } = require('./src/discord/webhook');
const sentBetsTracker = require('./src/services/sentBetsTracker');
const moment = require('moment-timezone');
const logger = require('./src/utils/logger');

async function testCheckModelBets() {
  const db = await getDb();
  const currentTime = moment().toDate();
  let modelEmbedsSent = 0;

  console.log('Testing checkModelBets with model_edges collection...');
  console.log('Current time:', currentTime);

  // Query the dedicated model_edges collection directly
  const collection = db.collection('model_edges');
  
  // First, check what we have
  const totalCount = await collection.countDocuments({
    timestamp: { $gt: currentTime }
  });
  console.log(`Total future model docs: ${totalCount}`);
  
  const highValueCount = await collection.countDocuments({
    $and: [
      { timestamp: { $gt: currentTime } },
      { edge_points: { $gte: 10 } },
      { ev: { $gte: 70 } }
    ]
  });
  console.log(`High value model docs to process: ${highValueCount}`);

  const cursor = collection.find({
    $and: [
      { timestamp: { $gt: currentTime } },
      { edge_points: { $gte: 10 } },
      { ev: { $gte: 70 } }
    ]
  }).limit(1); // Just test with one for now

  for await (const doc of cursor) {
    try {
      console.log('\nProcessing doc:');
      console.log(`  Game: ${doc.game}`);
      console.log(`  Edge points: ${doc.edge_points}`);
      console.log(`  EV: ${doc.ev}`);
      console.log(`  Book: ${doc.book}`);
      
      const modelKey = `model:${doc.projection_id}`;
      const wasModelSent = await sentBetsTracker.wasSent(modelKey);
      console.log(`  Was already sent: ${wasModelSent}`);
      
      if (!wasModelSent) {
        const embedContent = await createModelEmbed(doc);
        console.log('  Embed created successfully');
        console.log('  Title:', embedContent.embed.data.title);
        
        // Actually send the webhook
        console.log('  Sending webhook...');
        await sendWebhook('Model', embedContent, doc.book);
        await sentBetsTracker.markAsSent(modelKey, typeof doc.ev === 'number' ? doc.ev : null, null, null, 'model');
        modelEmbedsSent++;
        console.log('  ✅ Webhook sent successfully!');
      }
    } catch (err) {
      console.error('  ❌ Error processing model doc:', err);
    }
  }

  console.log(`\nTotal model embeds sent: ${modelEmbedsSent}`);
  process.exit(0);
}

// Initialize and run
sentBetsTracker.init().then(() => {
  testCheckModelBets();
});