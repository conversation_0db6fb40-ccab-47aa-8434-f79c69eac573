// Test why a specific bet isn't being sent
const { MongoClient } = require('mongodb');
const moment = require('moment-timezone');
const sentBetsTracker = require('./src/services/sentBetsTracker');
require('dotenv').config();

async function testSpecificBet() {
  const client = new MongoClient(process.env.MONGO_URI);
  
  try {
    await client.connect();
    await sentBetsTracker.init();
    
    const dbName = process.env.DB_NAME || 'edgezone_game_lines_dev';
    const db = client.db(dbName);
    const currentTime = moment().toDate();
    
    console.log('Current time:', currentTime);
    console.log('Looking for specific Colorado @ Georgia Tech bet...\n');
    
    const collection = db.collection('model_edges');
    
    // Find the specific bet
    const specificBet = await collection.findOne({
      projection_id: "model_ncaaf_fliff_ncaaf|20250830|colorado|georgia tech_full_game_spread_home_4.5"
    });
    
    if (specificBet) {
      console.log('Found the bet:');
      console.log('  Game:', specificBet.game);
      console.log('  Edge points:', specificBet.edge_points);
      console.log('  EV:', specificBet.ev);
      console.log('  Timestamp:', specificBet.timestamp);
      console.log('  Source:', specificBet.source);
      
      // Check filter criteria
      console.log('\nFilter checks:');
      console.log('  Timestamp > current?', specificBet.timestamp > currentTime);
      console.log('  Edge points < 10?', specificBet.edge_points < 10);
      console.log('  EV < 70?', specificBet.ev < 70);
      
      const meetsAllCriteria = 
        specificBet.timestamp > currentTime &&
        specificBet.edge_points < 10 &&
        specificBet.ev < 70;
      
      console.log('  Meets all criteria?', meetsAllCriteria);
      
      // Check if it was already sent
      const modelKey = `model:${specificBet.projection_id}`;
      const wasSent = await sentBetsTracker.wasSent(modelKey);
      console.log('\nWas already sent?', wasSent);
      
      if (wasSent) {
        // Check the sent bets collection
        const sentBetsCollection = db.collection('edgezone_sent_bets');
        const sentRecord = await sentBetsCollection.findOne({
          projection_id: modelKey
        });
        if (sentRecord) {
          console.log('Sent record found:');
          console.log('  Sent at:', sentRecord.sent_at);
          console.log('  Type:', sentRecord.type);
        }
      }
      
      // Check how many similar bets meet the criteria
      const similarCount = await collection.countDocuments({
        $and: [
          { timestamp: { $gt: currentTime } },
          { edge_points: { $lt: 10 } },
          { ev: { $lt: 70 } }
        ]
      });
      console.log('\nTotal bets meeting current filter criteria:', similarCount);
      
    } else {
      console.log('Bet not found in model_edges collection');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

testSpecificBet();