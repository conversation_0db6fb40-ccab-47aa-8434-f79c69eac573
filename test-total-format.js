// Test the formatting for over/under bets
const { WebhookClient, EmbedBuilder } = require('discord.js');
const moment = require('moment-timezone');
const config = require('./src/config');
const { normalizeName } = require('./src/config/discordRoles');

// Sample over/under document
const doc = {
  projection_id: "model_ncaaf_draftkings_ncaaf|20251018|georgia|ole miss_full_game_total_over_53.5",
  book: "draftkings",
  book_line: 53.5,
  book_price: -111,
  edge_points: 9.5,
  ev: 15.08,
  ev_display: "15.08%",
  game: "Georgia State @ Ole Miss",
  league: "NCAAF",
  market: "OVER_UNDER",
  market_type: "full_game_total",
  model_fair_line: 63,
  odds_american: -111,
  selection: "over",
  source: "ftntools-model",
  timestamp: new Date("2025-10-18T16:00:00.000Z")
};

// Create the model embed with new formatting
function createModelEmbed(doc) {
  const canonicalBook = normalizeName(doc.book) || doc.book;
  const authorIcon = config.images[canonicalBook] || config.images.edgezoneThumbnail;

  // Derive market label from doc.market or market_type
  let marketLabel = '';
  const marketLower = (doc.market || '').toLowerCase();
  const marketTypeLower = (doc.market_type || '').toLowerCase();
  
  // Check if this is a totals/over-under bet
  const isTotal = marketLower === 'over_under' || 
                  marketLower === 'total' || 
                  marketLower === 'gametotal' ||
                  marketTypeLower.includes('total');
  
  if (isTotal) {
    marketLabel = ''; // Don't add "Total" to the title for over/under
  } else if (marketLower === 'spread' || marketLower === 'gamespread' || marketTypeLower.includes('spread')) {
    marketLabel = 'Spread';
  } else if (marketLower === 'moneyline' || marketTypeLower.includes('money')) {
    marketLabel = 'Moneyline';
  } else if (doc.market) {
    marketLabel = doc.market;
  } else if (doc.market_type) {
    marketLabel = doc.market_type;
  }

  // Format selection - capitalize if it's a side like "away" or "home" or "over" or "under"
  const formattedSelection = doc.selection
    ? doc.selection.charAt(0).toUpperCase() + doc.selection.slice(1).toLowerCase()
    : '';

  // Add proper sign to lines
  const formatLine = (line) => {
    if (line == null) return null;
    const numLine = parseFloat(line);
    return numLine > 0 ? `+${line}` : `${line}`;
  };

  // Format timestamp
  const ts = doc.timestamp ? moment(doc.timestamp).tz('America/New_York').format('ddd, MMM D, h:mm A z') : '';
  
  // Build title based on market type
  let title;
  if (isTotal) {
    // For totals, format as "NCAAF - Over 53.5" (no market label needed)
    title = `${doc.league || ''} - ${formattedSelection} ${doc.book_line || ''}`.replace(/\s+/g, ' ').trim();
  } else {
    // For spreads and moneylines, include market label and formatted line
    const formattedBookLine = formatLine(doc.book_line);
    title = `${doc.league || ''} - ${formattedSelection} ${marketLabel || ''} ${formattedBookLine || ''}`.replace(/\s+/g, ' ').trim();
  }

  // Build description
  const description = `${doc.game || ''}${ts ? ' - ' + ts : ''}`;

  // Build the detailed field with model-specific information
  const fields = [{
    name: `${canonicalBook} Model`,
    value: [
      doc.odds_american != null ? `Bet Odds: ${doc.odds_american}` : (doc.book_price != null ? `Bet Odds: ${doc.book_price}` : null),
      doc.book_line != null ? `Book Line: ${isTotal ? doc.book_line : formatLine(doc.book_line)}` : null,
      doc.model_fair_line != null ? `Model Line: ${isTotal ? doc.model_fair_line : formatLine(doc.model_fair_line)}` : null,
      doc.ev_display ? `Edge: ${doc.ev_display}` : null,
      doc.edge_points != null ? `Edge Pts: ${doc.edge_points}` : null
    ].filter(Boolean).join('\n')
  }];

  const embed = new EmbedBuilder()
    .setColor('#00b894') // Distinct color for model embeds
    .setTitle(title)
    .setDescription(description)
    .setAuthor({ name: canonicalBook, iconURL: authorIcon })
    .setThumbnail(config.images.edgezoneThumbnail)
    .addFields(...fields)
    .setFooter({ text: 'Powered by Edge Zone', iconURL: config.images.edgezoneLogo || config.images.edgezoneThumbnail })
    .setTimestamp();

  return { embed };
}

const embedContent = createModelEmbed(doc);
console.log('Title:', embedContent.embed.data.title);
console.log('Description:', embedContent.embed.data.description);
console.log('Field:', embedContent.embed.data.fields[0]);

// Send the webhook to see the formatted result
async function sendTestWebhook() {
  try {
    const webhookUrl = 'https://discord.com/api/webhooks/1409997143485321308/h_57oDgCaSwFtNk6zLSsz0Vn2JmIY-WdO_2x80crMvfomCApacSZ-D4bosw6mBHnTccc';
    const webhookClient = new WebhookClient({ url: webhookUrl });
    
    await webhookClient.send({
      content: `NCAAF - Over 53.5 (-111) • Diff: 15.08%`,
      embeds: [embedContent.embed]
    });
    
    console.log('\n✅ Test webhook sent successfully!');
  } catch (error) {
    console.error('Error:', error);
  }
}

sendTestWebhook();