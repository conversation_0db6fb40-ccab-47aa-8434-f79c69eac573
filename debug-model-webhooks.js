// Debug script to check why model webhooks aren't being sent
const { MongoClient } = require('mongodb');
const moment = require('moment-timezone');
require('dotenv').config();

async function debugModelWebhooks() {
  const client = new MongoClient(process.env.MONGO_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const dbName = process.env.DB_NAME || 'edgezone_game_lines_dev';
    console.log('Database name:', dbName);
    
    const db = client.db(dbName);
    const currentTime = moment().toDate();
    console.log('Current time:', currentTime);
    console.log('---');
    
    // First, check if model_edges collection exists and has data
    console.log('Checking model_edges collection:');
    const modelEdgesCollection = db.collection('model_edges');
    const totalModelDocs = await modelEdgesCollection.countDocuments();
    console.log(`Total documents in model_edges: ${totalModelDocs}`);
    
    const futureModelDocs = await modelEdgesCollection.countDocuments({
      timestamp: { $gt: currentTime }
    });
    console.log(`Future games in model_edges: ${futureModelDocs}`);
    
    const highValueModelDocs = await modelEdgesCollection.countDocuments({
      $and: [
        { timestamp: { $gt: currentTime } },
        { edge_points: { $gte: 10 } },
        { ev: { $gte: 70 } }
      ]
    });
    console.log(`High value model docs (edge_points >= 10 AND ev >= 70): ${highValueModelDocs}`);
    
    // Show a sample of high value docs
    const sampleHighValue = await modelEdgesCollection.find({
      $and: [
        { timestamp: { $gt: currentTime } },
        { edge_points: { $gte: 10 } },
        { ev: { $gte: 70 } }
      ]
    }).limit(3).toArray();
    
    if (sampleHighValue.length > 0) {
      console.log('\nSample high value model docs:');
      sampleHighValue.forEach(doc => {
        console.log(`  - ${doc.game}: edge_points=${doc.edge_points}, ev=${doc.ev}, source=${doc.source}`);
      });
    }
    
    console.log('\n---');
    console.log('Checking regular collections for model docs:');
    
    // Check each regular collection for model docs
    const collections = [
      'Fanduel', 'DraftKings', 'Fliff', 'HardRock', 'Rebet', 
      'Bovada', 'Fanatics', 'NoVig', 'HighLimit', 'ProphetX', 
      'Underdog', 'Caesars', 'BetOnline'
    ];
    
    for (const collectionName of collections) {
      const collection = db.collection(collectionName);
      
      // Check for ANY model docs in this collection
      const modelDocsCount = await collection.countDocuments({
        source: 'ftntools-model'
      });
      
      if (modelDocsCount > 0) {
        console.log(`\n${collectionName}:`);
        console.log(`  Total model docs: ${modelDocsCount}`);
        
        const futureModelCount = await collection.countDocuments({
          $and: [
            { source: 'ftntools-model' },
            { timestamp: { $gt: currentTime } }
          ]
        });
        console.log(`  Future model docs: ${futureModelCount}`);
        
        const highValueCount = await collection.countDocuments({
          $and: [
            { source: 'ftntools-model' },
            { timestamp: { $gt: currentTime } },
            { edge_points: { $gte: 10 } },
            { ev: { $gte: 70 } }
          ]
        });
        console.log(`  High value model docs: ${highValueCount}`);
        
        // Show a sample
        const sample = await collection.findOne({ source: 'ftntools-model' });
        if (sample) {
          console.log(`  Sample: edge_points=${sample.edge_points}, ev=${sample.ev}, timestamp=${sample.timestamp}`);
        }
      }
    }
    
    console.log('\n---');
    console.log('DIAGNOSIS:');
    console.log('The checkModelBets function is looking for documents with source="ftntools-model"');
    console.log('in the regular sportsbook collections (Fanduel, DraftKings, etc.)');
    console.log('but the model documents appear to be in a separate "model_edges" collection.');
    console.log('\nThe function needs to be updated to query the model_edges collection directly.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

debugModelWebhooks();